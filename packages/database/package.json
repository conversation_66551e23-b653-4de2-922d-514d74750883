{"dependencies": {"@prisma/client": "^5.21.1", "zod": "^3.23.8", "zod-prisma-types": "^3.1.8"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@types/node": "22.8.4", "dotenv-cli": "^7.4.2", "prisma": "^5.21.1", "ts-node": "^10.9.2", "tsx": "^4.7.1", "typescript": "^5.6.3"}, "main": "./index.ts", "name": "database", "scripts": {"db:generate": "prisma generate", "db:push": "dotenv -c -e ../../.env -- prisma db push --skip-generate", "db:seed": "dotenv -c -e ../../.env -- prisma db seed", "db:studio": "dotenv -c -e ../../.env -- prisma studio", "type-check": "tsc --noEmit"}, "prisma": {"seed": "tsx prisma/seed.ts"}}