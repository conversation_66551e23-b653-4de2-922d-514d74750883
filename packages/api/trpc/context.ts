import { type Locale, config } from "@config";
import type { FetchCreateContextFnOptions } from "@trpc/server/adapters/fetch";
import { validateSessionToken } from "auth";
import { db } from "database";
import { cookies } from "next/headers";
import { getSignedUrl } from "storage";
import { defineAbilitiesFor } from "../modules/auth/abilities";

export async function createContext(
	params?: FetchCreateContextFnOptions | { isAdmin?: boolean },
) {
	console.log('=== DEBUG createContext ===');

	const cookieStore = await cookies();
	const sessionId =
		cookieStore.get(config.auth.sessionCookieName)?.value ?? null;

	console.log('Session ID from cookie:', sessionId);

	let user = null;
	let session = null;

	try {
		if (sessionId) {
			console.log('Validating session token...');
			console.log('Session ID type:', typeof sessionId);
			console.log('Session ID value:', sessionId);

			if (!sessionId || typeof sessionId !== 'string') {
				console.log('Invalid session ID, skipping validation');
				user = null;
				session = null;
			} else {
				const result = await validateSessionToken(sessionId);
				user = result.user;
				session = result.session;
				console.log('Session validation result:', { user: !!user, session: !!session });
			}
		} else {
			console.log('No session ID found in cookies');
		}
	} catch (error) {
		console.error("Erro ao validar token de sessão:", error);
		console.error("Error details:", error instanceof Error ? error.message : 'Unknown error');
		user = null;
		session = null;
	}

	console.log('User from session:', user ? { id: user.id, role: user.role } : null);

	let teamMemberships = null;

	// Só tenta acessar o banco se houver um usuário autenticado
	if (user) {
		try {
			console.log('Checking database connection...');
			// Verificar se o cliente do banco está disponível
			if (!db || typeof db.teamMembership === 'undefined') {
				console.error("Cliente do banco não está disponível");
				teamMemberships = null;
			} else {
				console.log('Database client is available, fetching team memberships...');
				// Adicionar timeout para evitar travamentos
				const timeoutPromise = new Promise((_, reject) => {
					setTimeout(() => reject(new Error("Timeout ao conectar com o banco")), 5000);
				});

				const dbPromise = db.teamMembership.findMany({
					where: {
						userId: user.id,
					},
					include: {
						team: true,
					},
				});

				console.log('Executing database query...');
				const memberships = await Promise.race([dbPromise, timeoutPromise]);
				console.log('Database query completed, memberships found:', Array.isArray(memberships) ? memberships.length : 'invalid result');

				// Verificar se o resultado é válido
				if (Array.isArray(memberships)) {
					console.log('Processing team memberships...');
					teamMemberships = await Promise.all(
						memberships.map(async (membership) => ({
							...membership,
							team: {
								...membership.team,
								avatarUrl: membership.team.avatarUrl
									? await getSignedUrl(membership.team.avatarUrl, {
											bucket: process.env.NEXT_PUBLIC_AVATARS_BUCKET_NAME as string,
											expiresIn: 360,
										})
									: null,
							},
						})),
					);
					console.log('Team memberships processed successfully');
				} else {
					console.error("Resultado inválido do banco:", memberships);
					teamMemberships = null;
				}
			}
		} catch (error) {
			console.error("Erro ao buscar team memberships:", error);
			teamMemberships = null;
		}
	}

	console.log('Final context state:', {
		user: !!user,
		session: !!session,
		teamMemberships: Array.isArray(teamMemberships) ? teamMemberships.length : 'null'
	});

	const abilities = defineAbilitiesFor({
		user,
		teamMemberships,
	});

	const locale = (cookieStore.get(config.i18n.localeCookieName)?.value ??
		config.i18n.defaultLocale) as Locale;

	return {
		user,
		session,
		locale,
		abilities,
		teamMemberships,
		responseHeaders:
			params && "resHeaders" in params ? params.resHeaders : undefined,
		isAdmin: params && "isAdmin" in params ? params.isAdmin : false,
	};
}

export type Context = Awaited<ReturnType<typeof createContext>>;
