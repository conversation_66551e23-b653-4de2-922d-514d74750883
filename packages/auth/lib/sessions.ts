import { config } from "@config";
import { sha256 } from "@oslojs/crypto/sha2";
import {
	encodeBase32LowerCaseNoPadding,
	encodeHexLowerCase,
} from "@oslojs/encoding";
import { type UserSession, db } from "database";

export function generateSessionToken(): string {
	const bytes = new Uint8Array(20);
	crypto.getRandomValues(bytes);
	const token = encodeBase32LowerCaseNoPadding(bytes);
	return token;
}

export async function createSession(
	token: string,
	userId: string,
	options?: {
		maxAge?: number;
		impersonatorId?: string | null;
	},
): Promise<UserSession> {
	if (!token || typeof token !== 'string') {
		throw new Error('Invalid token provided');
	}

	const sessionId = encodeHexLowerCase(sha256(new TextEncoder().encode(token)));
	const maxAge = options?.maxAge ?? config.auth.sessionCookieMaxAge;
	const impersonatorId = options?.impersonatorId ?? null;

	const session: UserSession = {
		id: sessionId,
		userId,
		expiresAt: new Date(Date.now() + maxAge * 1000),
		impersonatorId,
	};

	await db.userSession.create({
		data: session,
	});

	return session;
}

export async function validateSessionToken(token: string) {
	console.log('=== DEBUG validateSessionToken ===');
	console.log('Token received:', token);

	if (!token || typeof token !== 'string') {
		console.log('Invalid token provided:', { token, type: typeof token });
		return { session: null, user: null };
	}

	const sessionId = encodeHexLowerCase(sha256(new TextEncoder().encode(token)));
	console.log('Session ID hash:', sessionId);

	// First try to find the session without including the user
	const sessionOnly = await db.userSession.findUnique({
		where: {
			id: sessionId,
		},
	});

	console.log('Session found:', !!sessionOnly);
	if (sessionOnly) {
		console.log('Session expires at:', sessionOnly.expiresAt);
		console.log('Current time:', new Date());
		console.log('Session expired:', Date.now() >= sessionOnly.expiresAt.getTime());
	}

	if (sessionOnly === null) {
		console.log('No session found');
		return { session: null, user: null };
	}

	// Check if the session is expired
	if (Date.now() >= sessionOnly.expiresAt.getTime()) {
		console.log('Session expired, deleting...');
		await db.userSession.delete({ where: { id: sessionId } });
		return { session: null, user: null };
	}

	// Now try to get the user
	console.log('Getting user for session...');
	const user = await db.user.findUnique({
		where: {
			id: sessionOnly.userId,
		},
		select: {
			id: true,
			avatarUrl: true,
			email: true,
			emailVerified: true,
			name: true,
			onboardingComplete: true,
			role: true,
		},
	});

	console.log('User found:', !!user);
	if (user) {
		console.log('User details:', { id: user.id, role: user.role, email: user.email });
	}

	// If user doesn't exist, delete the session and return null
	if (!user) {
		console.log('User not found, deleting session...');
		await db.userSession.delete({ where: { id: sessionId } });
		return { session: null, user: null };
	}

	// Extend session if it's close to expiration
	if (Date.now() >= sessionOnly.expiresAt.getTime() - 1000 * 60 * 60 * 24 * 15) {
		console.log('Extending session...');
		sessionOnly.expiresAt = new Date(Date.now() + 1000 * 60 * 60 * 24 * 30);
		await db.userSession.update({
			where: {
				id: sessionOnly.id,
			},
			data: {
				expiresAt: sessionOnly.expiresAt,
			},
		});
	}

	console.log('Returning valid session and user');
	return { session: sessionOnly, user };
}

export async function invalidateSession(sessionId: string): Promise<void> {
	await db.userSession.delete({ where: { id: sessionId } });
}

export async function invalidateUserSessions(userId: string): Promise<void> {
	await db.userSession.deleteMany({ where: { userId } });
}

export type SessionUser = NonNullable<
	Awaited<ReturnType<typeof validateSessionToken>>["user"]
>;
