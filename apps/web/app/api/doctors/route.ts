import { NextResponse } from "next/server";
import { createApiCaller } from "api/trpc/caller";

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const specialty = searchParams.get("specialty");
    const search = searchParams.get("search");
    const available = searchParams.get("available");
    const pageParam = parseInt(searchParams.get("page") || "1", 10);
    const perPageParam = parseInt(searchParams.get("perPage") || "12", 10);

    const apiCaller = await createApiCaller();

    const result = await apiCaller.doctors.publicList({
      query: search || undefined,
      specialtyId: specialty || undefined,
      onlineOnly: available === "true",
      page: Number.isFinite(pageParam) && pageParam > 0 ? pageParam : 1,
      perPage: Number.isFinite(perPageParam) && perPageParam > 0 ? perPageParam : 12,
    });

    // Retornar os dados e paginação (sem mocks)
    return NextResponse.json({
      doctors: result.doctors,
      pagination: result.pagination,
      specialties: result.specialties,
      onlineCount: result.onlineCount,
    });
  } catch (error) {
    console.error("Error fetching doctors:", error);
    return NextResponse.json(
      { error: "Failed to fetch doctors" },
      { status: 500 }
    );
  }
}

function generateMockTimeSlots(): string[] {
  const slots = [];
  for (let hour = 8; hour <= 18; hour++) {
    if (hour !== 12) { // Pular horário de almoço
      slots.push(`${hour.toString().padStart(2, '0')}:00`);
    }
  }
  return slots;
}

function generateAvatarUrl(name: string): string {
  // Gerar avatar baseado no nome usando DiceBear
  const initials = name.split(' ').map(n => n[0]).join('').toUpperCase();
  return `https://api.dicebear.com/7.x/initials/svg?seed=${encodeURIComponent(initials)}&backgroundColor=4f46e5&textColor=ffffff`;
}
