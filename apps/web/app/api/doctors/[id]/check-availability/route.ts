import { NextResponse } from "next/server";
import { createApiCaller } from "api/trpc/caller";

export async function POST(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json();
    const { scheduledAt } = body;

    if (!scheduledAt) {
      return NextResponse.json(
        { error: "ScheduledAt parameter is required" },
        { status: 400 }
      );
    }

    // Consultar os time slots reais do dia para o médico e verificar disponibilidade
    const api = await createApiCaller();
    const date = new Date(scheduledAt);
    const ymd = date.toISOString().split("T")[0];
    const hhmm = date.toTimeString().slice(0, 5);

    const slots = await api.appointments.getTimeSlots({ doctorId: params.id, date: ymd });
    const slot = slots.find(s => new Date(s.startTime).toTimeString().slice(0,5) === hhmm);

    return NextResponse.json({ available: !!slot?.isAvailable });
  } catch (error) {
    console.error("Error checking availability:", error);
    return NextResponse.json(
      { error: "Failed to check availability" },
      { status: 500 }
    );
  }
}
