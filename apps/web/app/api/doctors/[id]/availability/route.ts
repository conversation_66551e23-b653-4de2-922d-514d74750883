import { NextResponse } from "next/server";
import { createApiCaller } from "api/trpc/caller";

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const { searchParams } = new URL(request.url);
    const date = searchParams.get("date");

    if (!date) {
      return NextResponse.json(
        { error: "Date parameter is required" },
        { status: 400 }
      );
    }

    const apiCaller = await createApiCaller();

    // Usar a procedure real para slots do dia
    const api = await createApiCaller();
    const slots = await api.appointments.getTimeSlots({ doctorId: params.id, date });

    return NextResponse.json(slots.map(s => ({
      time: new Date(s.startTime).toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' }),
      available: s.isAvailable,
      price: undefined,
      id: s.id,
    })));
  } catch (error) {
    console.error("Error fetching availability:", error);
    return NextResponse.json(
      { error: "Failed to fetch availability" },
      { status: 500 }
    );
  }
}
