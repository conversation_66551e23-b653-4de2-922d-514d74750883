import { NextRequest, NextResponse } from 'next/server';
import { currentUser } from '@saas/auth/lib/current-user';
import { PatientSubscriptionService } from '../../../../[locale]/(app)/patient/services/patient-subscription.service';
import { SUBSCRIPTION_PLANS } from '../../../../../../../packages/api/modules/subscriptions/schemas';

export async function GET(req: NextRequest) {
  try {
    const { user } = await currentUser();

    if (!user) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }

    const subscription = await PatientSubscriptionService.getActiveSubscription(user.id);

    if (!subscription) {
      return NextResponse.json({
        subscription: null,
        message: 'Nenhuma assinatura ativa encontrada'
      });
    }

    // Get plan features from the plan definition
    const planFeatures = SUBSCRIPTION_PLANS[subscription.planId as keyof typeof SUBSCRIPTION_PLANS]?.features || [];

    return NextResponse.json({
      subscription: {
        id: subscription.id,
        planId: subscription.planId,
        planName: subscription.planName,
        planPrice: subscription.planPrice,
        status: subscription.status,
        startDate: subscription.startDate,
        endDate: subscription.endDate,
        nextBillingDate: subscription.nextBillingDate,
        cycle: subscription.cycle,
        consultationsIncluded: subscription.consultationsIncluded,
        consultationsUsed: subscription.consultationsUsed,
        lastResetDate: subscription.lastResetDate,
        features: planFeatures
      }
    });
  } catch (error) {
    console.error('Erro ao buscar detalhes da assinatura:', error);
    return NextResponse.json({
      error: 'Erro interno do servidor',
      subscription: null
    }, { status: 500 });
  }
}
