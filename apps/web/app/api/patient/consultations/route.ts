import { NextRequest, NextResponse } from 'next/server';
import { db } from 'database';
import { currentUser } from '@saas/auth/lib/current-user';

export async function GET(request: NextRequest) {
  try {
    console.log('=== DEBUG CONSULTATIONS API ===');
    console.log('Request headers:', Object.fromEntries(request.headers.entries()));

    let user;
    try {
      const userResult = await currentUser();
      user = userResult?.user || userResult;
      console.log('User result from currentUser():', userResult);
    } catch (authError) {
      console.error('Erro na autenticação:', authError);
      return NextResponse.json({
        success: false,
        error: 'Erro de autenticação',
        details: authError instanceof Error ? authError.message : 'Erro desconhecido'
      }, { status: 401 });
    }

    console.log('User object:', JSON.stringify(user, null, 2));
    console.log('User role:', user?.role);
    console.log('User ID:', user?.id);
    console.log('===============================');

    if (!user) {
      console.log('No user found, returning 401');
      return NextResponse.json({
        success: false,
        error: 'Unauthorized'
      }, { status: 401 });
    }

    // Verificar se é um paciente
    if (user.role !== "PATIENT") {
      console.log('User role is not PATIENT:', user.role);
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    console.log('User authenticated successfully as PATIENT');

    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId') || user.id;
    const status = searchParams.get('status');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const offset = (page - 1) * limit;

    console.log('Buscando consultas para usuário:', userId, 'status:', status, 'página:', page);

    // Primeiro, buscar o registro do Patient usando o userId
    const patient = await db.patient.findUnique({
      where: { userId: userId },
      select: { id: true }
    });

    if (!patient) {
      console.log('Paciente não encontrado para userId:', userId);
      return NextResponse.json({
        success: true,
        data: [],
        pagination: {
          page,
          limit,
          total: 0,
          totalPages: 0
        }
      });
    }

    console.log('Paciente encontrado com ID:', patient.id);

    // Definir filtros baseados no status
    let whereCondition: any = {
      patientId: patient.id,
    };

    if (status === 'active') {
      whereCondition.status = {
        in: ['IN_PROGRESS', 'WAITING_ON_DUTY', 'ACCEPTED_BY_DOCTOR']
      };
    } else if (status === 'scheduled') {
      whereCondition.status = 'SCHEDULED';
      whereCondition.scheduledAt = {
        gte: new Date()
      };
    } else if (status === 'completed') {
      whereCondition.status = {
        in: ['COMPLETED', 'CANCELED', 'NO_SHOW']
      };
    }

    try {
      console.log('Executando consulta no banco com whereCondition:', JSON.stringify(whereCondition, null, 2));

      // Buscar consultas do banco com consulta simplificada
      const [appointments, totalCount] = await Promise.all([
        db.appointment.findMany({
          where: whereCondition,
          select: {
            id: true,
            status: true,
            scheduledAt: true,
            amount: true,
            paymentStatus: true,
            isOnDuty: true,
            urgencyLevel: true,
            consultType: true,
            appointmentType: true,
            symptoms: true,
            duration: true,
            roomId: true,
            chatEnabled: true,
            recordingEnabled: true,
            doctor: {
              select: {
                id: true,
                crm: true,
                onlineStatus: true,
                user: {
                  select: {
                    name: true,
                    avatarUrl: true
                  }
                },
                specialties: {
                  select: {
                    name: true
                  }
                }
              }
            }
          },
          orderBy: { scheduledAt: 'desc' },
          take: limit,
          skip: offset
        }),
        db.appointment.count({
          where: whereCondition
        })
      ]);

      console.log(`Encontradas ${appointments.length} consultas de ${totalCount} total`);

      // Transformar dados para o formato esperado
      const transformedAppointments = appointments.map((appointment, index) => {
        console.log(`Transformando consulta ${index + 1}:`, {
          id: appointment.id,
          status: appointment.status,
          hasDoctor: !!appointment.doctor,
          doctorName: appointment.doctor?.user?.name || appointment.doctor?.crm
        });
        try {
          // Verificar se o médico existe
          if (!appointment.doctor) {
            console.warn(`Consulta ${appointment.id} sem médico associado`);
            return null;
          }

          // Calcular mensagens não lidas (simplificado por enquanto)
          const unreadCount = 0;

          // Última mensagem (removido por enquanto para evitar erro de Prisma)
          const lastMessage = null;

          return {
            id: appointment.id || '',
            status: appointment.status || 'SCHEDULED',
            scheduledAt: appointment.scheduledAt instanceof Date ? appointment.scheduledAt.toISOString() : (appointment.scheduledAt || new Date().toISOString()),
            amount: appointment.amount ? Number(appointment.amount) : 0,
            paymentStatus: appointment.paymentStatus || 'PENDING',
            isOnDuty: appointment.isOnDuty || false,
            urgencyLevel: appointment.urgencyLevel || undefined,
            consultType: appointment.consultType || 'VIDEO',
            appointmentType: appointment.appointmentType || 'TELEMEDICINE',
            symptoms: appointment.symptoms || undefined,
            duration: appointment.duration || 30,
            doctor: {
              id: appointment.doctor.id || '',
              name: appointment.doctor.user?.name || appointment.doctor.crm || 'Médico não informado',
              specialty: appointment.doctor.specialties?.[0]?.name || 'Especialidade não informada',
              avatarUrl: appointment.doctor.user?.avatarUrl || null,
              crm: appointment.doctor.crm || 'CRM não informado',
              onlineStatus: appointment.doctor.onlineStatus || 'OFFLINE'
            },
            hospital: undefined,
            lastMessage: lastMessage || undefined,
            unreadCount: unreadCount || 0,
            roomId: appointment.roomId || undefined,
            chatEnabled: appointment.chatEnabled || false,
            recordingEnabled: appointment.recordingEnabled || false
          };
        } catch (transformError) {
          console.error('Erro ao transformar consulta:', appointment.id, transformError);
          return null;
        }
      }).filter(Boolean); // Remove itens null

      console.log('Retornando resposta da API:', {
        success: true,
        dataLength: transformedAppointments.length,
        pagination: {
          page,
          limit,
          total: totalCount,
          totalPages: Math.ceil(totalCount / limit)
        }
      });

      return NextResponse.json({
        success: true,
        data: transformedAppointments,
        pagination: {
          page,
          limit,
          total: totalCount,
          totalPages: Math.ceil(totalCount / limit)
        }
      });

    } catch (dbError) {
      console.error('Erro no banco de dados:', dbError);
      console.error('Stack trace:', dbError instanceof Error ? dbError.stack : 'No stack trace');
      return NextResponse.json(
        {
          success: false,
          error: 'Erro interno do servidor',
          details: dbError instanceof Error ? dbError.message : 'Erro desconhecido',
          stack: dbError instanceof Error ? dbError.stack : undefined
        },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Erro geral na API:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Erro interno do servidor',
        details: error instanceof Error ? error.message : 'Erro desconhecido'
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { user } = await currentUser();

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verificar se é um paciente
    if (user.role !== "PATIENT") {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    const body = await request.json();
    const { doctorId, scheduledAt, specialty, isOnDuty = false } = body;

    // Validar dados obrigatórios
    if (!doctorId || !scheduledAt) {
      return NextResponse.json(
        { error: 'doctorId e scheduledAt são obrigatórios' },
        { status: 400 }
      );
    }

    // Criar nova consulta
    const appointment = await db.appointment.create({
      data: {
        patientId: user.id,
        doctorId,
        scheduledAt: new Date(scheduledAt),
        status: isOnDuty ? 'WAITING_ON_DUTY' : 'SCHEDULED',
        isOnDuty,
        amount: isOnDuty ? 50 : 100, // Valores de exemplo
        paymentStatus: 'PENDING',
        consultType: 'VIDEO', // Valor válido do enum ConsultType
        duration: 30, // Valor padrão
        appointmentType: 'TELEMEDICINE' // Valor padrão
      },
      include: {
        doctor: {
          include: {
            specialties: true
          }
        },
        hospital: true
      }
    });

    // Transformar para o formato de resposta
    const transformedAppointment = {
      id: appointment.id,
      status: appointment.status,
      scheduledAt: appointment.scheduledAt,
      amount: appointment.amount ? Number(appointment.amount) : 0,
      paymentStatus: appointment.paymentStatus || 'PENDING',
      isOnDuty: appointment.isOnDuty,
      doctor: {
        id: appointment.doctor?.id,
        name: appointment.doctor?.crm || 'Médico não informado',
        specialty: appointment.doctor?.specialties?.[0]?.name || 'Especialidade não informada',
        avatarUrl: null,
        crm: appointment.doctor?.crm
      }
    };

    return NextResponse.json(transformedAppointment, { status: 201 });
  } catch (error) {
    console.error('Erro ao criar consulta:', error);
    return NextResponse.json(
      {
        error: 'Erro interno do servidor',
        details: error instanceof Error ? error.message : 'Erro desconhecido'
      },
      { status: 500 }
    );
  }
}
