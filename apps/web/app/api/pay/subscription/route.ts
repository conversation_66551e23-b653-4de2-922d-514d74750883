import { NextRequest, NextResponse } from 'next/server';
import { db } from 'database';
import { AsaasSubscriptionClient } from 'api/modules/subscriptions/asaas-subscription';
import { sendEmail } from 'mail';
import { EvolutionService } from '../../../../actions/checkout/integrations/evolution/evolution.service';
import { formatPhoneForWhatsApp } from '../../../../lib/utils/format-phone';
import { getBaseUrl } from 'utils';

export const runtime = 'nodejs';
export const dynamic = 'force-dynamic';

const PLANS = {
  'zapvida-sempre': {
    id: 'zapvida-sempre',
    name: 'ZapVida Sempre',
    price: 49.0,
    consultationsIncluded: 2,
    cycle: 'MONTHLY' as const,
  },
};

export async function POST(req: NextRequest) {
  try {
    const input = await req.json().catch(() => null);

    if (!input || typeof input !== 'object') {
      return NextResponse.json({ message: 'Corpo inválido' }, { status: 400 });
    }

    if (!input?.planId || !PLANS[input.planId]) {
      return NextResponse.json({ message: 'Plano inválido' }, { status: 400 });
    }
    if (!input?.customerData?.name || !input?.customerData?.email) {
      return NextResponse.json({ message: 'Nome e email são obrigatórios' }, { status: 400 });
    }

    const plan = PLANS[input.planId as keyof typeof PLANS];

    const safeCustomer = {
      name: String(input.customerData.name || ''),
      email: String(input.customerData.email || ''),
      phone: String(input.customerData.phone || ''),
      cpf: String(input.customerData.cpf || ''),
    };

    let user = await db.user.findUnique({ where: { email: safeCustomer.email }, include: { patient: true } });
    if (!user) {
      user = await db.user.create({
        data: {
          email: safeCustomer.email,
          name: safeCustomer.name,
          phone: safeCustomer.phone || null,
          role: 'PATIENT',
        },
        include: { patient: true },
      });
    }
    let patient = user.patient;
    if (!patient) {
      patient = await db.patient.create({
        data: {
          userId: user.id,
          cpf: safeCustomer.cpf?.replace(/\D/g, '') || `00000000000`,
          birthDate: new Date(),
          gender: 'N',
          address: {},
          allergies: [],
          chronicConditions: [],
        },
      });
    }

    const existing = await db.patientSubscription.findFirst({ where: { patientId: patient.id, status: 'ACTIVE' } });
    if (existing) {
      return NextResponse.json({ message: 'Você já possui uma assinatura ativa' }, { status: 409 });
    }

    const useMock = !process.env.ASAAS_API_KEY;

    let asaasCustomer: any = { id: 'mock_cus_' + patient.id.slice(0, 8) };
    if (!useMock) {
      const asaas = new AsaasSubscriptionClient();
      asaasCustomer = await asaas.createCustomer({
        name: safeCustomer.name,
        email: safeCustomer.email,
        cpfCnpj: safeCustomer.cpf?.replace(/\D/g, '') || '',
        phone: safeCustomer.phone?.replace(/\D/g, '') || '',
        mobilePhone: safeCustomer.phone?.replace(/\D/g, '') || '',
        notificationDisabled: true,
      });
    }

    const addMonths = (date: Date, months: number) => {
      const d = new Date(date.getTime());
      d.setMonth(d.getMonth() + months);
      return d;
    };
    const computePeriodEnd = (
      start: Date,
      cycle: 'WEEKLY' | 'BIWEEKLY' | 'MONTHLY' | 'QUARTERLY' | 'SEMIANNUALLY' | 'YEARLY'
    ) => {
      switch (cycle) {
        case 'WEEKLY':
          return new Date(start.getTime() + 7 * 24 * 60 * 60 * 1000);
        case 'BIWEEKLY':
          return new Date(start.getTime() + 14 * 24 * 60 * 60 * 1000);
        case 'QUARTERLY':
          return addMonths(start, 3);
        case 'SEMIANNUALLY':
          return addMonths(start, 6);
        case 'YEARLY':
          return addMonths(start, 12);
        case 'MONTHLY':
        default:
          return addMonths(start, 1);
      }
    };

    const cycle = plan.cycle;
    const currentPeriodStart = new Date();
    // Para cobrar imediatamente a primeira mensalidade, definir nextDueDate para hoje
    const nextDueDateStr = new Date().toISOString().split('T')[0];

    const billingType: 'CREDIT_CARD' | 'PIX' | 'BOLETO' = input.paymentMethod === 'PIX' ? 'PIX' : 'CREDIT_CARD';

    const subscriptionData: any = {
      customer: asaasCustomer.id,
      billingType,
      value: plan.price,
      cycle: plan.cycle,
      nextDueDate: nextDueDateStr,
    };

    if (billingType === 'CREDIT_CARD') {
      const cc = input.creditCard || {};
      if (!cc.cardNumber || !cc.cardHolder || !cc.cardExpiry || !cc.cardCvv) {
        return NextResponse.json({ message: 'Dados de cartão inválidos' }, { status: 400 });
      }
      const [expiryMonth, expiryYear2] = String(cc.cardExpiry).split('/');
      const expiryYear = expiryYear2?.length === 2 ? `20${expiryYear2}` : expiryYear2;

      subscriptionData.creditCard = {
        holderName: String(cc.cardHolder),
        number: String(cc.cardNumber).replace(/\D/g, ''),
        expiryMonth: String(expiryMonth || ''),
        expiryYear: String(expiryYear || ''),
        ccv: String(cc.cardCvv || ''),
      };
      subscriptionData.creditCardHolderInfo = {
        name: safeCustomer.name,
        email: safeCustomer.email,
        cpfCnpj: safeCustomer.cpf?.replace(/\D/g, ''),
        postalCode: '01311000',
        addressNumber: '123',
        phone: safeCustomer.phone?.replace(/\D/g, ''),
        mobilePhone: safeCustomer.phone?.replace(/\D/g, ''),
      };
    }

    console.log('[PAY/SUBSCRIPTION] Payload to Asaas:', JSON.stringify({
      ...subscriptionData,
      creditCard: subscriptionData.creditCard ? { ...subscriptionData.creditCard, number: '****' } : undefined,
    }));

    let asaasSub: any = { id: 'mock_sub_' + patient.id.slice(0, 8), nextDueDate: nextDueDateStr };
    let firstPaymentId: string | null = null;
    let firstPaymentStatus: string | null = null;
    let pixCode: { encodedImage: string; payload: string } | null = null;
    if (!useMock) {
      const asaas = new AsaasSubscriptionClient();
      try {
        asaasSub = await asaas.createSubscription(subscriptionData);
        console.log('[PAY/SUBSCRIPTION] Assinatura criada no Asaas:', {
          subscriptionId: asaasSub.id,
          status: asaasSub.status,
          nextDueDate: asaasSub.nextDueDate
        });

        // Buscar o primeiro pagamento gerado para verificar se foi cobrado imediatamente
        try {
          const payments = await asaas.listSubscriptionPayments({
            subscriptionId: asaasSub.id,
            limit: 1,
            offset: 0
          });
          const payment = payments?.data?.[0];
          if (payment?.id) {
            firstPaymentId = payment.id;
            firstPaymentStatus = payment.status;
            console.log('[PAY/SUBSCRIPTION] Primeiro pagamento encontrado:', {
              paymentId: payment.id,
              status: payment.status,
              value: payment.value,
              dueDate: payment.dueDate,
              billingType: payment.billingType
            });

            // Se for PIX, gerar QR Code
            if (billingType === 'PIX') {
              try {
                pixCode = await asaas.getPixQRCode(payment.id);
              } catch (pixErr) {
                console.error('[PAY/SUBSCRIPTION] Erro ao obter PIX da assinatura:', pixErr);
              }
            }
          } else {
            console.warn('[PAY/SUBSCRIPTION] Nenhum pagamento encontrado para a assinatura:', asaasSub.id);
          }
        } catch (paymentsErr) {
          console.error('[PAY/SUBSCRIPTION] Erro ao buscar pagamentos da assinatura:', paymentsErr);
        }
      } catch (e: any) {
        console.error('[PAY/SUBSCRIPTION] ASAAS error:', {
          message: e?.message,
          status: e?.status,
          endpoint: e?.endpoint,
          method: e?.method,
          body: e?.body,
          response: e?.response,
        });
        const isDev = process.env.NODE_ENV !== 'production';
        return NextResponse.json({
          message: 'Erro ao criar assinatura no provedor',
          ...(isDev ? {
            provider: 'asaas',
            status: e?.status,
            endpoint: e?.endpoint,
            method: e?.method,
            response: e?.response,
          } : {}),
        }, { status: 502 });
      }
    }

    // Determinar status inicial baseado no status do primeiro pagamento
    let initialStatus: 'ACTIVE' | 'PENDING' = 'PENDING';

    if (billingType === 'CREDIT_CARD') {
      // Para cartão de crédito, verificar se o primeiro pagamento foi confirmado
      if (firstPaymentStatus === 'CONFIRMED' || firstPaymentStatus === 'RECEIVED') {
        initialStatus = 'ACTIVE';
        console.log('[PAY/SUBSCRIPTION] Pagamento com cartão confirmado, assinatura ativada');
      } else if (firstPaymentStatus === 'PENDING') {
        initialStatus = 'PENDING';
        console.log('[PAY/SUBSCRIPTION] Pagamento com cartão pendente, assinatura aguardando');
      } else {
        // Se não temos status do pagamento, assumir ativo para cartão (comportamento padrão)
        initialStatus = 'ACTIVE';
        console.log('[PAY/SUBSCRIPTION] Status do pagamento não disponível, assumindo ativo para cartão');
      }
    } else if (billingType === 'PIX') {
      // PIX sempre inicia como PENDING até ser pago
      initialStatus = 'PENDING';
      console.log('[PAY/SUBSCRIPTION] Pagamento PIX criado, assinatura pendente');
    }

    console.log('[PAY/SUBSCRIPTION] Status inicial da assinatura:', {
      billingType,
      firstPaymentStatus,
      initialStatus
    });

    // Persistir assinatura no banco
    const subscription = await db.patientSubscription.create({
      data: {
        patientId: patient.id,
        planId: plan.id,
        planName: plan.name,
        planPrice: plan.price as any,
        asaasSubscriptionId: asaasSub.id,
        status: initialStatus as any,
        startDate: new Date(),
        endDate: null,
        nextBillingDate: new Date((asaasSub as any).nextDueDate || nextDueDateStr),
        consultationsIncluded: plan.consultationsIncluded,
        consultationsUsed: 0,
        lastResetDate: new Date(),
        paymentMethod: billingType as any,
      } as any,
    });

    // Para PIX, criar Transaction para tracking do primeiro pagamento
    let transaction = null;
    if (billingType === 'PIX' && firstPaymentId) {
      transaction = await db.transaction.create({
        data: {
          patientSubscriptionId: subscription.id,
          amount: plan.price as any,
          platformFee: 0,
          doctorAmount: 0,
          status: 'PENDING',
          paymentMethod: 'PIX',
          asaasId: firstPaymentId,
          dueDate: new Date(),
        },
      });
    }

    // Só atualizar hasActiveSubscription se a subscription estiver ACTIVE
    if (initialStatus === 'ACTIVE') {
      await db.patient.update({ where: { id: patient.id }, data: { hasActiveSubscription: true } });
    }

    // Gerar magic link para login automático (sempre, mesmo para PIX)
    let magicUrl = null;
    try {
      const baseUrl = getBaseUrl();
      const magicLinkResponse = await fetch(`${baseUrl}/api/auth/magic-link`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userId: user.id,
          type: 'DASHBOARD',
          redirectTo: '/app'
        })
      });

      if (magicLinkResponse.ok) {
        const magicLinkData = await magicLinkResponse.json();
        magicUrl = magicLinkData.magicUrl;
        console.log('[PAY/SUBSCRIPTION] Magic link gerado para assinatura:', !!magicUrl);
      }
    } catch (magicError) {
      console.error('[PAY/SUBSCRIPTION] Erro ao gerar magic link:', magicError);
    }

    // Enviar notificações de forma assíncrona (não bloquear resposta)
    if (initialStatus === 'ACTIVE') {
      // Executar notificações em background sem await
      setImmediate(async () => {
        try {
          // Email
          await sendEmail({
            to: user.email,
            templateId: "subscriptionCreated" as any,
            context: {
              recipientName: user.name || 'Paciente',
              planName: plan.name,
              planPrice: plan.price,
              nextBillingDate: subscription.nextBillingDate,
              manageUrl: magicUrl || `${getBaseUrl()}/app`,
            } as any,
            locale: 'pt' as any,
          });

          // WhatsApp (se tiver telefone)
          if (user.phone) {
            const evolution = new EvolutionService();
            const phone = formatPhoneForWhatsApp(user.phone);
            const accessUrl = magicUrl || `${getBaseUrl()}/app`;
            const messages: string[] = [
              `Olá ${user.name || 'Paciente'}! Sua assinatura ${plan.name} foi ativada com sucesso. ✅`,
              `Valor do plano: R$ ${Number(plan.price).toFixed(2)}${subscription.nextBillingDate ? `\nPróxima cobrança: ${new Date(subscription.nextBillingDate).toLocaleDateString('pt-BR')}` : ''}`,
              `🔗 **Acesse sua conta agora:**\n${accessUrl}\n\n*${magicUrl ? 'LOGIN AUTOMÁTICO: Clique no link acima para acessar diretamente!' : 'Faça login para gerenciar sua assinatura'}*`
            ];
            await evolution.sendMessagesWithDelay(messages, phone);
          }
        } catch (notifErr) {
          console.error('[PAY/SUBSCRIPTION] Erro ao enviar notificações de assinatura:', notifErr);
        }
      });
    }

    // Importante: para PIX recorrente, o Asaas retorna um paymentLink para autorização do Pix Automático
    // Exponha este link para o frontend redirecionar o usuário
    return NextResponse.json({
      success: true,
      subscriptionId: subscription.id,
      mocked: useMock,
      paymentId: firstPaymentId,
      paymentStatus: firstPaymentStatus,
      pixCode,
      status: initialStatus,
      transactionId: transaction?.id,
      magicUrl, // Incluir magic URL para login automático
      paymentCharged: firstPaymentStatus === 'CONFIRMED' || firstPaymentStatus === 'RECEIVED',
    });
  } catch (error: any) {
    console.error('[PAY/SUBSCRIPTION] Error:', error);
    return NextResponse.json({ message: error?.message || 'Erro no processamento' }, { status: 500 });
  }
}


