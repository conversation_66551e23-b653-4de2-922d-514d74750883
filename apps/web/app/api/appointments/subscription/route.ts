import { NextRequest, NextResponse } from 'next/server';
import { createSubscriptionAppointment, cancelSubscriptionAppointment } from '../../../../actions/appointments/create-subscription-appointment';
import { getMonthlyConsultationSummary } from '../../../../lib/subscription-utils';
import { currentUser } from '@saas/auth/lib/current-user';
import { db } from 'database';

export const runtime = 'nodejs';
export const dynamic = 'force-dynamic';

// Criar consulta via subscription
export async function POST(req: NextRequest) {
  try {
    const input = await req.json();

    if (!input.doctorId || !input.scheduledAt) {
      return NextResponse.json(
        { message: 'Dados obrigatórios: doctorId, scheduledAt' },
        { status: 400 }
      );
    }

    const result = await createSubscriptionAppointment({
      doctorId: input.doctorId,
      timeSlotId: input.timeSlotId,
      scheduledAt: new Date(input.scheduledAt),
      duration: input.duration,
      consultType: input.consultType,
      symptoms: input.symptoms,
    });

    return NextResponse.json(result);

  } catch (error: any) {
    console.error('[API/APPOINTMENTS/SUBSCRIPTION] POST Error:', error);
    return NextResponse.json(
      { message: error.message || 'Erro ao agendar consulta via assinatura' },
      { status: 400 }
    );
  }
}

// Cancelar consulta via subscription
export async function DELETE(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const appointmentId = searchParams.get('appointmentId');

    if (!appointmentId) {
      return NextResponse.json(
        { message: 'appointmentId é obrigatório' },
        { status: 400 }
      );
    }

    const result = await cancelSubscriptionAppointment(appointmentId);

    return NextResponse.json(result);

  } catch (error: any) {
    console.error('[API/APPOINTMENTS/SUBSCRIPTION] DELETE Error:', error);
    return NextResponse.json(
      { message: error.message || 'Erro ao cancelar consulta' },
      { status: 400 }
    );
  }
}

// Obter resumo de consultas mensais
export async function GET(req: NextRequest) {
  try {
    const session = await currentUser();
    if (!session?.user) {
      return NextResponse.json(
        { message: 'Usuário não autenticado' },
        { status: 401 }
      );
    }

    const patient = await db.patient.findFirst({
      where: { userId: session.user.id },
    });

    if (!patient) {
      return NextResponse.json(
        { message: 'Paciente não encontrado' },
        { status: 404 }
      );
    }

    const summary = await getMonthlyConsultationSummary(patient.id);

    return NextResponse.json(summary);

  } catch (error: any) {
    console.error('[API/APPOINTMENTS/SUBSCRIPTION] GET Error:', error);
    return NextResponse.json(
      { message: error.message || 'Erro ao obter resumo de consultas' },
      { status: 500 }
    );
  }
}
