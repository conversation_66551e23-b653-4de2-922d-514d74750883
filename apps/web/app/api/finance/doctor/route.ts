import { NextRequest, NextResponse } from 'next/server';
import { currentUser } from '@saas/auth/lib/current-user';
import { db } from 'database';
import { PaymentStatusSchema, PaymentMethodSchema, type PaymentStatusType, type PaymentMethodType } from 'database';

export async function GET(request: NextRequest) {
  try {
    const { user } = await currentUser();

    if (!user) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }

    if (user.role !== 'DOCTOR') {
      return NextResponse.json({ error: 'Acesso negado' }, { status: 403 });
    }

    // Buscar o médico pelo userId
    const doctor = await db.doctor.findFirst({
      where: { userId: user.id },
      select: { id: true },
    });

    if (!doctor) {
      return NextResponse.json({ error: 'Médico não encontrado' }, { status: 404 });
    }

    const searchParams = request.nextUrl.searchParams;
    const period = searchParams.get('period') || '30days';
    const status = searchParams.get('status');
    const method = searchParams.get('method');

    // Calcular data de início baseada no período
    const now = new Date();
    let startDate = new Date();

    switch (period) {
      case 'today':
        startDate.setHours(0, 0, 0, 0);
        break;
      case '7days':
        startDate.setDate(now.getDate() - 7);
        break;
      case '30days':
        startDate.setDate(now.getDate() - 30);
        break;
      case '3months':
        startDate.setMonth(now.getMonth() - 3);
        break;
      case '6months':
        startDate.setMonth(now.getMonth() - 6);
        break;
      case '1year':
        startDate.setFullYear(now.getFullYear() - 1);
        break;
      default:
        startDate.setDate(now.getDate() - 30);
    }

    // Construir filtros
    const where: any = {
      doctorId: doctor.id,
      createdAt: {
        gte: startDate,
        lte: now,
      },
    };

    if (status && status !== 'all') {
      where.status = status as PaymentStatus;
    }

    if (method && method !== 'all') {
      where.paymentMethod = method as PaymentMethod;
    }

    // Buscar transações
    const transactions = await db.transaction.findMany({
      where,
      include: {
        appointment: {
          include: {
            patient: {
              include: {
                user: {
                  select: {
                    name: true,
                    email: true,
                  },
                },
              },
            },
          },
        },
      },
      orderBy: { createdAt: 'desc' },
    });

    // Calcular métricas
    const totalRevenue = transactions
      .filter(t => t.status === 'PAID')
      .reduce((sum, t) => sum + Number(t.doctorAmount), 0);

    const monthlyRevenue = transactions
      .filter(t => {
        const transactionDate = new Date(t.createdAt);
        const currentMonth = new Date();
        return (
          t.status === 'PAID' &&
          transactionDate.getMonth() === currentMonth.getMonth() &&
          transactionDate.getFullYear() === currentMonth.getFullYear()
        );
      })
      .reduce((sum, t) => sum + Number(t.doctorAmount), 0);

    const paidAppointments = transactions.filter(t => t.status === 'PAID').length;
    const totalAppointments = transactions.length;
    const conversionRate = totalAppointments > 0 ? (paidAppointments / totalAppointments) * 100 : 0;
    const averageTicket = paidAppointments > 0 ? totalRevenue / paidAppointments : 0;

    // Dados para gráfico de evolução (últimos 30 dias)
    const chartData = [];
    for (let i = 29; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      const dayStart = new Date(date);
      dayStart.setHours(0, 0, 0, 0);
      const dayEnd = new Date(date);
      dayEnd.setHours(23, 59, 59, 999);

      const dayTransactions = transactions.filter(t => {
        const tDate = new Date(t.createdAt);
        return tDate >= dayStart && tDate <= dayEnd && t.status === 'PAID';
      });

      const dayRevenue = dayTransactions.reduce((sum, t) => sum + Number(t.doctorAmount), 0);
      const dayAppointments = dayTransactions.length;

      chartData.push({
        date: date.toISOString().split('T')[0],
        revenue: dayRevenue,
        appointments: dayAppointments,
      });
    }

    // Distribuição por método de pagamento
    const paymentMethods = ['CREDIT_CARD', 'PIX', 'BOLETO'].map(method => {
      const methodTransactions = transactions.filter(t =>
        t.paymentMethod === method && t.status === 'PAID'
      );
      return {
        method,
        count: methodTransactions.length,
        amount: methodTransactions.reduce((sum, t) => sum + Number(t.doctorAmount), 0),
      };
    }).filter(pm => pm.count > 0);

    // Formatar transações para resposta
    const formattedTransactions = transactions.map(t => ({
      id: t.id,
      amount: Number(t.amount),
      doctorAmount: Number(t.doctorAmount),
      platformFee: Number(t.platformFee),
      status: t.status,
      paymentMethod: t.paymentMethod,
      paidAt: t.paidAt?.toISOString(),
      createdAt: t.createdAt.toISOString(),
      patientName: t.appointment?.patient?.user?.name || 'Paciente',
      appointmentDate: t.appointment?.scheduledAt?.toISOString(),
    }));

    return NextResponse.json({
      metrics: {
        totalRevenue,
        monthlyRevenue,
        paidAppointments,
        conversionRate: Math.round(conversionRate * 100) / 100,
        averageTicket,
      },
      chartData,
      paymentMethods,
      transactions: formattedTransactions,
    });

  } catch (error) {
    console.error('Error fetching doctor financial data:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
