import { NextRequest, NextResponse } from 'next/server';
import { headers } from 'next/headers';
import type { EvolutionWebhookEvent } from '@lib/evolution/types';

/**
 * Webhook handler for Evolution API events
 * This endpoint receives real-time events from the Evolution API
 */
export async function POST(request: NextRequest) {
  try {
    // Verify request authenticity (you might want to add API key validation)
    const headersList = headers();
    const contentType = headersList.get('content-type');

    if (!contentType?.includes('application/json')) {
      return NextResponse.json(
        { error: 'Invalid content type' },
        { status: 400 }
      );
    }

    // Parse webhook payload
    const payload: EvolutionWebhookEvent = await request.json();

    console.log('Evolution Webhook Event:', {
      event: payload.event,
      instance: payload.instance,
      timestamp: new Date(payload.timestamp * 1000),
    });

    // Handle different event types
    switch (payload.event) {
      case 'message':
        await handleMessageEvent(payload);
        break;

      case 'group_update':
        await handleGroupUpdateEvent(payload);
        break;

      case 'connection.update':
        await handleConnectionUpdateEvent(payload);
        break;

      case 'status':
        await handleStatusEvent(payload);
        break;

      default:
        console.log('Unhandled event type:', payload.event);
    }

    return NextResponse.json({ success: true });

  } catch (error) {
    console.error('Evolution webhook error:', error);

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * Handle incoming/outgoing message events
 */
async function handleMessageEvent(payload: EvolutionWebhookEvent) {
  const messageData = payload.data;

  // Log message for monitoring
  console.log('Message event:', {
    from: messageData.key?.remoteJid,
    fromMe: messageData.key?.fromMe,
    messageType: messageData.messageType,
    timestamp: new Date(payload.timestamp * 1000),
  });

  // Here you could:
  // 1. Store message in database for history
  // 2. Trigger auto-responses based on content
  // 3. Update patient/doctor status based on message
  // 4. Send notifications to relevant parties

  // Example: Auto-respond to specific keywords
  if (!messageData.key?.fromMe && messageData.message?.conversation) {
    const messageText = messageData.message.conversation.toLowerCase();

    if (messageText.includes('urgente') || messageText.includes('emergência')) {
      // Handle urgent messages - could trigger alerts
      console.log('Urgent message detected:', messageText);

      // You could send alerts to medical staff here
      // await notifyMedicalStaff(messageData);
    }
  }
}

/**
 * Handle group update events (member added/removed, etc.)
 */
async function handleGroupUpdateEvent(payload: EvolutionWebhookEvent) {
  const groupData = payload.data;

  console.log('Group update event:', {
    groupId: groupData.id,
    action: groupData.action,
    participants: groupData.participants,
    timestamp: new Date(payload.timestamp * 1000),
  });

  // Here you could:
  // 1. Update local group member cache
  // 2. Send welcome message to new members
  // 3. Log member changes for audit
  // 4. Update doctor availability based on group membership
}

/**
 * Handle connection status changes
 */
async function handleConnectionUpdateEvent(payload: EvolutionWebhookEvent) {
  const connectionData = payload.data;

  console.log('Connection update:', {
    instance: payload.instance,
    state: connectionData.state,
    timestamp: new Date(payload.timestamp * 1000),
  });

  // Here you could:
  // 1. Update system status dashboard
  // 2. Send alerts if connection is lost
  // 3. Trigger reconnection attempts
  // 4. Log uptime/downtime for monitoring

  if (connectionData.state === 'close') {
    // Connection lost - send alert to admins
    console.warn('WhatsApp connection lost for instance:', payload.instance);

    // You could send email/SMS alerts to administrators
    // await sendAdminAlert('WhatsApp connection lost');
  }
}

/**
 * Handle status events (online/offline, etc.)
 */
async function handleStatusEvent(payload: EvolutionWebhookEvent) {
  const statusData = payload.data;

  console.log('Status event:', {
    instance: payload.instance,
    status: statusData,
    timestamp: new Date(payload.timestamp * 1000),
  });

  // Here you could:
  // 1. Update user presence status
  // 2. Track online/offline patterns
  // 3. Adjust notification strategies based on status
}

/**
 * Example function to notify medical staff about urgent messages
 */
async function notifyMedicalStaff(messageData: any) {
  // This is where you'd integrate with your existing notification system
  console.log('Notifying medical staff about urgent message');

  // Example: Send to plantão group
  // const evolutionIntegration = createPlantaoEvolutionIntegration();
  // if (evolutionIntegration) {
  //   await evolutionIntegration.notifyUrgentCase({
  //     patientName: 'Paciente via WhatsApp',
  //     reason: 'Mensagem urgente recebida',
  //     patientPhone: messageData.key.remoteJid,
  //     patientId: 'whatsapp-' + Date.now(),
  //     systemUrl: process.env.NEXT_PUBLIC_SITE_URL || '',
  //   });
  // }
}

/**
 * Example function to send admin alerts
 */
async function sendAdminAlert(message: string) {
  console.log('Admin alert:', message);

  // Here you could:
  // 1. Send email to administrators
  // 2. Send SMS alerts
  // 3. Create system notifications
  // 4. Log to monitoring system
}
