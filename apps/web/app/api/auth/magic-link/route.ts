import { NextRequest, NextResponse } from 'next/server';
import { db } from 'database';
import { generateVerificationToken } from 'auth/lib/tokens';
import { getBaseUrl } from 'utils';

export async function POST(req: NextRequest) {
  try {
    const { transactionId, appointmentId, type = 'DASHBOARD', redirectTo, userId } = await req.json();

    if (!transactionId && !appointmentId && !userId) {
      return NextResponse.json(
        { success: false, message: 'Transaction ID, Appointment ID ou User ID é obrigatório' },
        { status: 400 }
      );
    }

    console.log('[MAGIC_LINK] Gerando magic link:', { transactionId, appointmentId, type, userId });

    let appointment;
    let user;

    if (userId) {
      // Buscar usuário diretamente (para assinaturas)
      user = await db.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          email: true,
          name: true
        }
      });

      if (!user) {
        return NextResponse.json(
          { success: false, message: 'Usu<PERSON>rio não encontrado' },
          { status: 404 }
        );
      }
    } else if (appointmentId) {
      // Buscar diretamente pelo appointmentId
      appointment = await db.appointment.findUnique({
        where: { id: appointmentId },
        include: {
          patient: {
            include: {
              user: {
                select: {
                  id: true,
                  email: true,
                  name: true
                }
              }
            }
          }
        }
      });

      if (!appointment) {
        return NextResponse.json(
          { success: false, message: 'Appointment não encontrado' },
          { status: 404 }
        );
      }

      user = appointment.patient?.user;
    } else {
      // Buscar pela transaction (fluxo antigo)
      const transaction = await db.transaction.findUnique({
        where: { id: transactionId },
        include: {
          appointment: {
            include: {
              patient: {
                include: {
                  user: {
                    select: {
                      id: true,
                      email: true,
                      name: true
                    }
                  }
                }
              }
            }
          }
        }
      });

      if (!transaction || !transaction.appointment) {
        return NextResponse.json(
          { success: false, message: 'Transação não encontrada' },
          { status: 404 }
        );
      }

      appointment = transaction.appointment;
      user = appointment.patient?.user;
    }

    if (!user) {
      return NextResponse.json(
        { success: false, message: 'Usuário não encontrado' },
        { status: 404 }
      );
    }

    // Gerar token de verificação (válido por 2 horas)
    const magicToken = await generateVerificationToken({
      userId: user.id,
      expireDuration: 2 * 60 * 60 * 1000 // 2 horas
    });

    // Construir URL do magic link
    const baseUrl = getBaseUrl();
    let redirectPath;

    // Se redirectTo foi fornecido, usar ele
    if (redirectTo) {
      redirectPath = redirectTo;
    } else {
      // Caso contrário, usar lógica padrão baseada no tipo
      switch (type) {
        case 'CONSULTATION':
          redirectPath = `/chats/${appointment.id}`;
          break;
        case 'PLANTAO':
          redirectPath = `/patient/zapchat/${appointment.id}`;
          break;
        case 'DASHBOARD':
        default:
          redirectPath = '/app';
          break;
      }
    }

    const magicUrl = `${baseUrl}/api/auth/magic-login?token=${magicToken}&redirect=${encodeURIComponent(redirectPath)}`;

    console.log('[MAGIC_LINK] Magic link gerado com sucesso:', {
      userId: user.id,
      appointmentId: appointment?.id || null,
      type,
      tokenLength: magicToken.length
    });

    return NextResponse.json({
      success: true,
      magicUrl,
      user: {
        id: user.id,
        name: user.name,
        email: user.email
      },
      appointment: appointment ? {
        id: appointment.id,
        status: appointment.status,
        isOnDuty: appointment.isOnDuty,
        urgencyLevel: appointment.urgencyLevel
      } : null
    });

  } catch (error) {
    console.error('[MAGIC_LINK] Erro ao gerar magic link:', error);
    return NextResponse.json(
      {
        success: false,
        message: error instanceof Error ? error.message : 'Erro interno'
      },
      { status: 500 }
    );
  }
}
