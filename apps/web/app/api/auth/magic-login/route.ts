import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { validateVerificationToken } from 'auth/lib/tokens';
import { generateSessionToken, createSession } from 'auth/lib/sessions';
import { createSessionCookie } from 'auth/lib/cookies';
import { getBaseUrl } from 'utils';

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const token = searchParams.get('token');
    const redirect = searchParams.get('redirect') || '/app';

    if (!token) {
      console.error('[MAGIC_LOGIN] Token não fornecido');
      return NextResponse.redirect(new URL('/auth/login?error=invalid_token', getBaseUrl()));
    }

    console.log('[MAGIC_LOGIN] Validando magic token:', {
      tokenLength: token.length,
      redirect
    });

    // Validar o token de verificação
    let userId: string;
    try {
      userId = await validateVerificationToken({ token });
    } catch (error) {
      console.error('[MAGIC_LOGIN] Token inválido ou expirado:', error);
      return NextResponse.redirect(new URL('/auth/login?error=invalid_or_expired_token', getBaseUrl()));
    }

    console.log('[MAGIC_LOGIN] Token válido, criando sessão para usuário:', userId);

    // Gerar novo token de sessão
    const sessionToken = generateSessionToken();

    // Criar sessão no banco
    await createSession(sessionToken, userId, {
      maxAge: 30 * 24 * 60 * 60 // 30 dias
    });

    // Criar cookie de sessão
    const cookieStore = await cookies();
    cookieStore.set(createSessionCookie(sessionToken));

    console.log('[MAGIC_LOGIN] Sessão criada com sucesso, redirecionando para:', redirect);

    // Redirecionar para o destino
    return NextResponse.redirect(new URL(redirect, getBaseUrl()));

  } catch (error) {
    console.error('[MAGIC_LOGIN] Erro interno:', error);
    return NextResponse.redirect(new URL('/auth/login?error=internal_error', getBaseUrl()));
  }
}
