import { currentUser } from "@saas/auth/lib/current-user";
import { redirect } from "@i18n/routing";
import { getLocale } from "next-intl/server";
import { MobileDashboard } from "./components/mobile-dashboard";
import { getPatientDashboardData } from "../../../../../actions/patients/get-dashboard-data";

export const dynamic = "force-dynamic";

export default async function PatientDashboardPage() {
	const locale = await getLocale();
	const { user } = await currentUser();

	if (!user) {
		return redirect({ href: "/auth/login", locale });
	}

	const dashboardData = await getPatientDashboardData();

	return <MobileDashboard user={user} data={dashboardData} />;
}
