"use client";

import type { User } from 'database';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "../../components/ui/card";
import { Button } from "../../components/ui/button";
import { Heart, TrendingUp, Calendar, FileText, Activity } from "lucide-react";

interface HealthSummaryProps {
	user: User;
	metrics?: {
		lastConsultation: string;
		totalConsultations: number;
		activePrescriptions: number;
		upcomingExams?: number;
	};
}

export function HealthSummary({ user, metrics }: HealthSummaryProps) {
	const healthMetrics = metrics ?? {
		lastConsultation: "—",
		totalConsultations: 0,
		activePrescriptions: 0,
		upcomingExams: 0,
	};

	return (
		<Card>
			<CardHeader className="pb-3">
				<CardTitle className="text-lg flex items-center gap-2">
					<Heart className="w-5 h-5 text-red-500" />
					Resumo de Saúde
				</CardTitle>
			</CardHeader>
			<CardContent>
				<div className="space-y-4">
					{/* Métricas principais */}
					<div className="grid grid-cols-2 gap-4">
						<div className="text-center p-4 bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl border border-blue-200">
							<div className="text-3xl font-bold text-blue-600 mb-1">
								{healthMetrics.totalConsultations}
							</div>
							<div className="text-xs text-blue-700 font-medium">
								Consultas realizadas
							</div>
						</div>

						<div className="text-center p-4 bg-gradient-to-br from-green-50 to-green-100 rounded-xl border border-green-200">
							<div className="text-3xl font-bold text-green-600 mb-1">
								{healthMetrics.activePrescriptions}
							</div>
							<div className="text-xs text-green-700 font-medium">
								Prescrições ativas
							</div>
						</div>
					</div>

					{/* Informações detalhadas */}
					<div className="space-y-3">
						<div className="flex items-center justify-between text-sm">
							<div className="flex items-center gap-2">
								<Calendar className="w-4 h-4 text-gray-500" />
								<span className="text-gray-600">Última consulta</span>
							</div>
							<span className="font-medium">{healthMetrics.lastConsultation}</span>
						</div>

						{(healthMetrics.upcomingExams ?? 0) > 0 && (
							<div className="flex items-center justify-between text-sm">
								<div className="flex items-center gap-2">
									<Activity className="w-4 h-4 text-gray-500" />
									<span className="text-gray-600">Exames pendentes</span>
								</div>
								<span className="font-medium text-orange-600">
									{healthMetrics.upcomingExams}
								</span>
							</div>
						)}
					</div>

					{/* Ações relacionadas à saúde */}
					<div className="space-y-2 pt-2 border-t">
						<Button
							variant="outline"
							fullWidth
							className="justify-start h-auto py-3"
						>
							<FileText className="w-4 h-4 mr-2" />
							<div className="text-left">
								<div className="font-medium">Ver Histórico Médico</div>
								<div className="text-xs text-gray-500">
									Consultas, exames e prescrições
								</div>
							</div>
						</Button>

						<Button
							variant="outline"
							fullWidth
							className="justify-start h-auto py-3"
						>
							<TrendingUp className="w-4 h-4 mr-2" />
							<div className="text-left">
								<div className="font-medium">Monitoramento de Saúde</div>
								<div className="text-xs text-gray-500">
									Acompanhe indicadores e metas
								</div>
							</div>
						</Button>
					</div>

					{/* Alerta ou lembrete */}
					{/* <div className="bg-gradient-to-r from-purple-50 to-pink-50 border border-purple-200 rounded-xl p-4">
						<div className="flex items-start gap-3">
							<div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center flex-shrink-0">
								<Heart className="w-4 h-4 text-white" />
							</div>
							<div className="text-sm">
								<p className="font-semibold text-purple-900 mb-1">
									💡 Lembrete de Saúde
								</p>
								<p className="text-purple-700 text-xs leading-relaxed">
									Que tal agendar um check-up? Sua última consulta foi há {healthMetrics.lastConsultation}.
								</p>
							</div>
						</div>
					</div> */}
				</div>
			</CardContent>
		</Card>
	);
}
