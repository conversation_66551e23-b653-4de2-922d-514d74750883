"use client";

import type { User } from 'database';
import { <PERSON>, <PERSON><PERSON>ontent, CardH<PERSON>er, CardTitle } from "../../components/ui/card";
import { ActionCard } from "../../components/ui/action-card";
import { StatusBadge } from "../../components/ui/status-badge";
import {
	Calendar,
	Clock,
	Heart,
	Plus,
	FileText,
	Shield,
	MessageSquare,
	Stethoscope,
	CreditCard,
	ArrowRight,
	Phone,
	Video,
	User as UserIcon,
	Settings,
	Bell,
	HelpCircle,
	BookOpen,
	MapPin,
	Star,
	TrendingUp,
	Activity
} from "lucide-react";
import { ActiveConsultationCard } from "./active-consultation-card";
import { QuickActions } from "./quick-actions";
import { HealthSummary } from "./health-summary";
import Link from "next/link";
import type { PatientDashboardData } from "../../../../../../actions/patients/get-dashboard-data";

interface MobileDashboardProps {
	user: User;
	data: PatientDashboardData;
}

export function MobileDashboard({ user, data }: MobileDashboardProps) {
	const rawActive = data.upcomingAppointments.find(a => a.status === "IN_PROGRESS" || (a.isOnDuty && (a.status === "SCHEDULED" || a.status === "IN_PROGRESS"))) || null;
	const activeConsultation = rawActive ? {
		id: rawActive.id,
		status: rawActive.status === "IN_PROGRESS" ? "IN_PROGRESS" : "WAITING",
		doctorName: rawActive.doctor?.name ?? "Médico",
		specialties: (rawActive.doctor?.specialties ?? []).map(s => s.name),
		queueTime: Math.max(1, Math.floor((Date.now() - new Date(rawActive.createdAt).getTime()) / 60000)),
		estimatedWaitTime: rawActive.status === "SCHEDULED" ? 15 : undefined,
		type: (rawActive.isOnDuty ? "ON_DUTY" : "VIDEO") as "ON_DUTY" | "VIDEO" | "CHAT",
	} : null;
	const upcomingAppointmentsCount = data.upcomingAppointments.filter(a => a.status === "SCHEDULED").length;
	const hasSubscription = data.subscription.hasActive;
	const subscriptionStatus = data.subscription.hasActive ? "ACTIVE" : "PENDING";
	const unreadNotifications = 3;
	const recentActivity = 5;

	const getGreeting = () => {
		const hour = new Date().getHours();
		if (hour < 12) return "Bom dia";
		if (hour < 18) return "Boa tarde";
		return "Boa noite";
	};

	return (
		<div className="container mx-auto max-w-6xl px-4 py-6">
			<div className="min-h-screen bg-gray-50">
				{/* Conteúdo principal */}
				<div className="space-y-4">
					{/* Saudação compacta */}
					<div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-4 border border-blue-100">
						<div className="flex items-center justify-between">
							<div className="flex items-center space-x-3">
								<div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center">
									<span className="text-white text-lg font-semibold">
										{user.name?.charAt(0).toUpperCase()}
									</span>
								</div>
								<div>
									<h1 className="text-lg font-semibold text-gray-900">
										{getGreeting()}, {user.name?.split(" ")[0]}!
									</h1>
									<p className="text-sm text-gray-600">
										Como podemos ajudá-lo hoje?
									</p>
								</div>
							</div>
							<div className="text-2xl">👋</div>
						</div>
					</div>

					{/* Consulta ativa - se houver */}
					{activeConsultation && (
						<ActiveConsultationCard consultation={activeConsultation} />
					)}

					{/* Ações rápidas */}
					<QuickActions />

					{/* Resumo de saúde */}
					<HealthSummary
						user={user}
						metrics={{
							lastConsultation: data.totals.lastConsultationAt ? new Intl.DateTimeFormat('pt-BR').format(new Date(data.totals.lastConsultationAt)) : "—",
							totalConsultations: data.totals.totalConsultations,
							activePrescriptions: data.totals.activePrescriptions,
							upcomingExams: 0,
						}}
					/>

					{/* Status da assinatura */}
					<Card>
						<CardHeader className="pb-3">
							<div className="flex items-center justify-between">
								<CardTitle className="text-lg flex items-center gap-2">
									<Shield className="w-5 h-5 text-blue-600" />
									Sua Assinatura
								</CardTitle>
								<StatusBadge status={subscriptionStatus} />
							</div>
						</CardHeader>
						<CardContent>
							<div className="space-y-3">
								<p className="text-sm text-gray-600">
									{hasSubscription
										? "Aproveite 2 consultas por mês e benefícios exclusivos"
										: "Assine e tenha acesso a 2 consultas por mês"
									}
								</p>

								<div className="flex items-center justify-between text-sm">
									<span className="text-gray-500">Próxima cobrança</span>
									<span className="font-medium">
										{data.subscription.nextBillingDate ? new Intl.DateTimeFormat('pt-BR').format(new Date(data.subscription.nextBillingDate)) : "—"}
									</span>
								</div>

								<ActionCard
									title="Gerenciar Assinatura"
									description="Alterar plano, formas de pagamento e mais"
									icon={CreditCard}
									href="/patient/subscriptions"
									className="mt-3"
								/>
							</div>
						</CardContent>
					</Card>

					{/* Próximas consultas */}
					<Card>
						<CardHeader className="pb-3">
							<div className="flex items-center justify-between">
								<CardTitle className="text-lg flex items-center gap-2">
									<Calendar className="w-5 h-5 text-blue-600" />
									Próximas Consultas
								</CardTitle>
								<Link
									href="/patient/appointments"
									className="text-sm text-blue-600 hover:text-blue-700 flex items-center gap-1"
								>
									Ver todas
									<ArrowRight className="w-4 h-4" />
								</Link>
							</div>
						</CardHeader>
						<CardContent>
							<div className="space-y-3">
								{upcomingAppointmentsCount > 0 ? (
									<>
										<div className="flex items-center justify-between">
											<span className="text-sm text-gray-600">
												Você tem {upcomingAppointmentsCount} consultas agendadas
											</span>
											<span className="bg-blue-100 text-blue-700 px-2 py-1 rounded-full text-xs font-medium">
												{upcomingAppointmentsCount}
											</span>
										</div>

										<ActionCard
											title="Ver Todas as Consultas"
											description="Acompanhe seus agendamentos e histórico"
											icon={Calendar}
											href="/patient/appointments"
										/>
									</>
								) : (
									<>
										<p className="text-sm text-gray-600">
											Nenhuma consulta agendada no momento
										</p>

										<ActionCard
											title="Agendar Nova Consulta"
											description="Escolha um médico e horário que funciona para você"
											icon={Plus}
											primary
											href="/patient/schedule"
										/>
									</>
								)}
							</div>
						</CardContent>
					</Card>





					{/* Spacer para navegação inferior */}
					<div className="h-4" />

					{/* Footer útil para o paciente */}
					<footer className="mt-4 px-4 py-4 bg-gray-50 rounded-lg shadow flex flex-col items-center gap-2 text-center">
						<span className="text-sm text-gray-700 font-medium">
							Precisa de ajuda? Fale com nosso suporte 24h.
						</span>
						<a
							href="/patient/support"
							className="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-semibold hover:bg-blue-700 transition"
						>
							Acessar Suporte
						</a>
						<span className="text-xs text-gray-400 mt-2">
							© {new Date().getFullYear()} ZapVida Saúde. Todos os direitos reservados.
						</span>
					</footer>
				</div>
			</div>
		</div>
	);
}
