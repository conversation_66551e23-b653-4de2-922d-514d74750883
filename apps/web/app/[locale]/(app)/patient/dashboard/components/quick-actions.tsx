"use client";

import { ActionCard } from "../../components/ui/action-card";
import { PlantaoActionCard } from "../../components/ui/plantao-action-card";
import { Card, CardContent, CardHeader, CardTitle } from "../../components/ui/card";
import {
	Calendar,
	Clock,
	Stethoscope,
	MessageSquare,
	Phone,
	Video,
	FileText
} from "lucide-react";
import Link from "next/link";

export function QuickActions() {
	return (
		<Card>
			<CardHeader className="pb-3">
				<CardTitle className="text-lg">Ações Rápidas</CardTitle>
			</CardHeader>
			<CardContent>
				{/* Grid responsivo: 2 colunas no mobile, mantendo o layout do desktop */}
				<div className="grid grid-cols-2 gap-3 sm:gap-4">
					{/* Consulta Agendada */}
					<Link href="/patient/schedule" className="block">
						<div className="bg-white rounded-lg border border-gray-200 p-3 sm:p-4 hover:shadow-md transition-all duration-200 hover:border-blue-300 cursor-pointer group active:scale-95">
							<div className="flex flex-col items-center text-center space-y-2 sm:space-y-3">
								<div className="w-10 h-10 sm:w-12 sm:h-12 bg-blue-100 rounded-lg sm:rounded-xl flex items-center justify-center group-hover:bg-blue-200 transition-colors">
									<Calendar className="w-5 h-5 sm:w-6 sm:h-6 text-blue-600" />
								</div>
								<div>
									<h3 className="font-semibold text-gray-900 text-sm sm:text-base group-hover:text-blue-700 transition-colors">Agendar</h3>
									<p className="text-xs sm:text-sm text-gray-500 mt-1 leading-tight px-1 sm:px-0">
										Escolha data e horário
									</p>
								</div>
							</div>
						</div>
					</Link>

					{/* Plantão Médico */}
					<Link href="/pay/plantao" className="block">
						<div className="bg-white rounded-lg border border-gray-200 p-3 sm:p-4 hover:shadow-md transition-all duration-200 hover:border-red-300 cursor-pointer group active:scale-95">
							<div className="flex flex-col items-center text-center space-y-2 sm:space-y-3">
								<div className="w-10 h-10 sm:w-12 sm:h-12 bg-red-100 rounded-lg sm:rounded-xl flex items-center justify-center group-hover:bg-red-200 transition-colors">
									<Clock className="w-5 h-5 sm:w-6 sm:h-6 text-red-600" />
								</div>
								<div>
									<h3 className="font-semibold text-gray-900 text-sm sm:text-base group-hover:text-red-700 transition-colors">Plantão</h3>
									<p className="text-xs sm:text-sm text-gray-500 mt-1 leading-tight px-1 sm:px-0">
										Atendimento imediato
									</p>
								</div>
							</div>
						</div>
					</Link>

					{/* ZapChat - Consulta Completa */}
					<Link href="/patient/zapchat" className="block">
						<div className="bg-white rounded-lg border border-gray-200 p-3 sm:p-4 hover:shadow-md transition-all duration-200 hover:border-green-300 cursor-pointer group active:scale-95">
							<div className="flex flex-col items-center text-center space-y-2 sm:space-y-3">
								<div className="w-10 h-10 sm:w-12 sm:h-12 bg-green-100 rounded-lg sm:rounded-xl flex items-center justify-center group-hover:bg-green-200 transition-colors">
									<MessageSquare className="w-5 h-5 sm:w-6 sm:h-6 text-green-600" />
								</div>
								<div>
									<h3 className="font-semibold text-gray-900 text-sm sm:text-base group-hover:text-green-700 transition-colors">ZapChat</h3>
									<p className="text-xs sm:text-sm text-gray-500 mt-1 leading-tight px-1 sm:px-0">
										Chat, vídeo e áudio
									</p>
								</div>
							</div>
						</div>
					</Link>

					{/* Prescrições */}
					<Link href="/patient/prescriptions" className="block">
						<div className="bg-white rounded-lg border border-gray-200 p-3 sm:p-4 hover:shadow-md transition-all duration-200 hover:border-purple-300 cursor-pointer group active:scale-95">
							<div className="flex flex-col items-center text-center space-y-2 sm:space-y-3">
								<div className="w-10 h-10 sm:w-12 sm:h-12 bg-purple-100 rounded-lg sm:rounded-xl flex items-center justify-center group-hover:bg-purple-200 transition-colors">
									<FileText className="w-5 h-5 sm:w-6 sm:h-6 text-purple-600" />
								</div>
								<div>
									<h3 className="font-semibold text-gray-900 text-sm sm:text-base group-hover:text-purple-700 transition-colors">Prescrições</h3>
									<p className="text-xs sm:text-sm text-gray-500 mt-1 leading-tight px-1 sm:px-0">
										Receitas e documentos
									</p>
								</div>
							</div>
						</div>
					</Link>
				</div>

				{/* Destaque para ação principal */}
				<div className="mt-4 sm:mt-6 p-3 sm:p-4 bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg border border-blue-200">
					<div className="flex items-center justify-between">
						<div className="flex-1 min-w-0">
							<h4 className="font-semibold text-blue-900 text-sm sm:text-base">
								Precisa de atendimento urgente?
							</h4>
							<p className="text-xs sm:text-sm text-blue-700 mt-1">
								Nosso plantão 24h está disponível
							</p>
						</div>
						<div className="flex-shrink-0 ml-2 sm:ml-3">
							<div className="w-8 h-8 sm:w-10 sm:h-10 bg-red-100 rounded-full flex items-center justify-center">
								<Stethoscope className="w-4 h-4 sm:w-5 sm:h-5 text-red-600" />
							</div>
						</div>
					</div>
				</div>
			</CardContent>
		</Card>
	);
}
