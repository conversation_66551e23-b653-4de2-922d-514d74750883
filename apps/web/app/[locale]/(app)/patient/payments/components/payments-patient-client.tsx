"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON>er, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import {
	CreditCard,
	Calendar,
	Download,
	Filter,
	Search,
	CheckCircle,
	XCircle,
	Clock,
	Receipt,
	DollarSign,
	TrendingUp,
	AlertCircle
} from "lucide-react";
import { User } from "@prisma/client";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";

interface PaymentsPatientClientProps {
	user: User;
}

interface Payment {
	id: string;
	description: string;
	amount: number;
	status: "PAID" | "PENDING" | "FAILED" | "REFUNDED";
	date: string;
	dueDate?: string;
	method: string;
	invoiceUrl?: string;
	type: "SUBSCRIPTION" | "CONSULTATION" | "ON_DUTY" | "OTHER";
}

interface PaymentSummary {
	totalPaid: number;
	totalPending: number;
	monthlyAverage: number;
	lastPayment: string;
}

export function PaymentsPatientClient({ user }: PaymentsPatientClientProps) {
	const [payments, setPayments] = useState<Payment[]>([]);
	const [filteredPayments, setFilteredPayments] = useState<Payment[]>([]);
	const [summary, setSummary] = useState<PaymentSummary | null>(null);
	const [isLoading, setIsLoading] = useState(true);
	const [statusFilter, setStatusFilter] = useState<string>("ALL");
	const [typeFilter, setTypeFilter] = useState<string>("ALL");
	const [searchTerm, setSearchTerm] = useState("");

	// Carregar pagamentos reais do paciente
	useEffect(() => {
		let isMounted = true;
		const load = async () => {
			try {
				const resp = await fetch('/api/patient/transactions');
				const data = await resp.json();
				if (!isMounted) return;
				setPayments(data.transactions || []);
				if (data.summary) {
					setSummary({
						totalPaid: data.summary.totalPaid || 0,
						totalPending: data.summary.totalPending || 0,
						monthlyAverage: data.summary.monthlyAverage || 0,
						lastPayment: data.summary.lastPayment || new Date().toISOString(),
					});
				}
			} catch (e) {
				console.error('Erro ao carregar pagamentos do paciente', e);
				setPayments([]);
				setSummary(null);
			} finally {
				if (isMounted) setIsLoading(false);
			}
		};
		load();
		return () => { isMounted = false; };
	}, []);

	// Filtrar pagamentos
	useEffect(() => {
		let filtered = payments;

		if (statusFilter !== "ALL") {
			filtered = filtered.filter(payment => payment.status === statusFilter);
		}

		if (typeFilter !== "ALL") {
			filtered = filtered.filter(payment => payment.type === typeFilter);
		}

		if (searchTerm) {
			filtered = filtered.filter(payment =>
				payment.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
				payment.method.toLowerCase().includes(searchTerm.toLowerCase())
			);
		}

		setFilteredPayments(filtered);
	}, [payments, statusFilter, typeFilter, searchTerm]);

	const getStatusBadge = (status: string) => {
		switch (status) {
			case "PAID":
				return (
					<Badge className="bg-green-100 text-green-800">
						<CheckCircle className="w-3 h-3 mr-1" />
						Pago
					</Badge>
				);
			case "PENDING":
				return (
					<Badge className="bg-yellow-100 text-yellow-800">
						<Clock className="w-3 h-3 mr-1" />
						Pendente
					</Badge>
				);
			case "FAILED":
				return (
					<Badge variant="destructive">
						<XCircle className="w-3 h-3 mr-1" />
						Falhou
					</Badge>
				);
			case "REFUNDED":
				return (
					<Badge variant="secondary">
						<Receipt className="w-3 h-3 mr-1" />
						Reembolsado
					</Badge>
				);
			default:
				return null;
		}
	};

	const getTypeLabel = (type: string) => {
		switch (type) {
			case "SUBSCRIPTION":
				return "Assinatura";
			case "CONSULTATION":
				return "Consulta";
			case "ON_DUTY":
				return "Plantão";
			case "OTHER":
				return "Outros";
			default:
				return type;
		}
	};

	const handleDownloadInvoice = (payment: Payment) => {
		if (payment.invoiceUrl) {
			// Simular download
			const link = document.createElement('a');
			link.href = payment.invoiceUrl;
			link.download = `nota-fiscal-${payment.id}.pdf`;
			link.click();
		}
	};

	if (isLoading) {
		return (
			<div className="container mx-auto py-6 px-4">
				<div className="space-y-6">
					{/* Summary skeleton */}
					<div className="grid grid-cols-1 md:grid-cols-4 gap-4">
						{[1, 2, 3, 4].map(i => (
							<Card key={i} className="animate-pulse">
								<CardContent className="p-6">
									<div className="space-y-3">
										<div className="h-4 bg-gray-200 rounded w-1/2" />
										<div className="h-6 bg-gray-200 rounded w-3/4" />
									</div>
								</CardContent>
							</Card>
						))}
					</div>
					{/* Payments skeleton */}
					<Card className="animate-pulse">
						<CardContent className="p-6">
							<div className="space-y-4">
								{[1, 2, 3].map(i => (
									<div key={i} className="flex items-center justify-between">
										<div className="space-y-2">
											<div className="h-4 bg-gray-200 rounded w-48" />
											<div className="h-3 bg-gray-200 rounded w-32" />
										</div>
										<div className="h-6 bg-gray-200 rounded w-20" />
									</div>
								))}
							</div>
						</CardContent>
					</Card>
				</div>
			</div>
		);
	}

	return (
		<div className="container mx-auto py-6 px-4 max-w-6xl">
			{/* Header */}
			<div className="mb-8">
				<h1 className="text-2xl font-bold text-gray-900 mb-2">
					Histórico de Pagamentos
				</h1>
				<p className="text-gray-600">
					Acompanhe todos os seus pagamentos e faturas
				</p>
			</div>

			{/* Summary Cards */}
			{summary && (
				<div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
					<Card>
						<CardContent className="p-6">
							<div className="flex items-center justify-between">
								<div>
									<p className="text-sm font-medium text-gray-600">Total Pago</p>
									<p className="text-2xl font-bold text-green-600">
										R$ {summary.totalPaid.toFixed(2)}
									</p>
								</div>
								<div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
									<DollarSign className="w-6 h-6 text-green-600" />
								</div>
							</div>
						</CardContent>
					</Card>

					<Card>
						<CardContent className="p-6">
							<div className="flex items-center justify-between">
								<div>
									<p className="text-sm font-medium text-gray-600">Pendente</p>
									<p className="text-2xl font-bold text-yellow-600">
										R$ {summary.totalPending.toFixed(2)}
									</p>
								</div>
								<div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center">
									<Clock className="w-6 h-6 text-yellow-600" />
								</div>
							</div>
						</CardContent>
					</Card>

					<Card>
						<CardContent className="p-6">
							<div className="flex items-center justify-between">
								<div>
									<p className="text-sm font-medium text-gray-600">Média Mensal</p>
									<p className="text-2xl font-bold text-blue-600">
										R$ {summary.monthlyAverage.toFixed(2)}
									</p>
								</div>
								<div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
									<TrendingUp className="w-6 h-6 text-blue-600" />
								</div>
							</div>
						</CardContent>
					</Card>

					<Card>
						<CardContent className="p-6">
							<div className="flex items-center justify-between">
								<div>
									<p className="text-sm font-medium text-gray-600">Último Pagamento</p>
									<p className="text-lg font-bold text-gray-900">
										{format(new Date(summary.lastPayment), "dd/MM", { locale: ptBR })}
									</p>
									<p className="text-xs text-gray-500">
										{format(new Date(summary.lastPayment), "yyyy", { locale: ptBR })}
									</p>
								</div>
								<div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
									<Calendar className="w-6 h-6 text-purple-600" />
								</div>
							</div>
						</CardContent>
					</Card>
				</div>
			)}

			{/* Filters and Search */}
			<Card className="mb-6">
				<CardContent className="p-6">
					<div className="flex flex-col md:flex-row gap-4">
						{/* Search */}
						<div className="relative flex-1">
							<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
							<input
								type="text"
								placeholder="Buscar pagamentos..."
								value={searchTerm}
								onChange={(e) => setSearchTerm(e.target.value)}
								className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
							/>
						</div>

						{/* Status Filter */}
						<select
							value={statusFilter}
							onChange={(e) => setStatusFilter(e.target.value)}
							className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
						>
							<option value="ALL">Todos os Status</option>
							<option value="PAID">Pagos</option>
							<option value="PENDING">Pendentes</option>
							<option value="FAILED">Falharam</option>
							<option value="REFUNDED">Reembolsados</option>
						</select>

						{/* Type Filter */}
						<select
							value={typeFilter}
							onChange={(e) => setTypeFilter(e.target.value)}
							className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
						>
							<option value="ALL">Todos os Tipos</option>
							<option value="SUBSCRIPTION">Assinatura</option>
							<option value="CONSULTATION">Consulta</option>
							<option value="ON_DUTY">Plantão</option>
							<option value="OTHER">Outros</option>
						</select>
					</div>
				</CardContent>
			</Card>

			{/* Payments List */}
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<Receipt className="w-5 h-5" />
						Histórico de Transações
					</CardTitle>
				</CardHeader>
				<CardContent>
					{filteredPayments.length > 0 ? (
						<div className="space-y-4">
							{filteredPayments.map(payment => (
								<div key={payment.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
									<div className="flex-1">
										<div className="flex items-center gap-3 mb-2">
											<h3 className="font-semibold text-gray-900">
												{payment.description}
											</h3>
											{getStatusBadge(payment.status)}
											<Badge variant="outline" className="text-xs">
												{getTypeLabel(payment.type)}
											</Badge>
										</div>
										<div className="flex items-center gap-4 text-sm text-gray-600">
											<span className="flex items-center gap-1">
												<Calendar className="w-3 h-3" />
												{format(new Date(payment.date), "dd/MM/yyyy 'às' HH:mm", { locale: ptBR })}
											</span>
											<span className="flex items-center gap-1">
												<CreditCard className="w-3 h-3" />
												{payment.method}
											</span>
											{payment.dueDate && payment.status === "PENDING" && (
												<span className="flex items-center gap-1 text-orange-600">
													<AlertCircle className="w-3 h-3" />
													Vence em {format(new Date(payment.dueDate), "dd/MM", { locale: ptBR })}
												</span>
											)}
										</div>
									</div>

									<div className="flex items-center gap-4">
										<div className="text-right">
											<p className="text-lg font-bold text-gray-900">
												R$ {payment.amount.toFixed(2)}
											</p>
										</div>

										{payment.invoiceUrl && payment.status === "PAID" && (
											<Button
												size="sm"
												variant="outline"
												onClick={() => handleDownloadInvoice(payment)}
											>
												<Download className="w-4 h-4 mr-2" />
												Nota Fiscal
											</Button>
										)}

										{payment.status === "PENDING" && (
											<Button size="sm">
												Pagar Agora
											</Button>
										)}

										{payment.status === "FAILED" && (
											<Button size="sm" variant="outline">
												Tentar Novamente
											</Button>
										)}
									</div>
								</div>
							))}
						</div>
					) : (
						<div className="text-center py-12">
							<div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
								<Receipt className="w-8 h-8 text-gray-400" />
							</div>
							<h3 className="text-lg font-semibold text-gray-900 mb-2">
								Nenhum pagamento encontrado
							</h3>
							<p className="text-gray-600">
								{searchTerm || statusFilter !== "ALL" || typeFilter !== "ALL"
									? "Tente ajustar os filtros de busca"
									: "Você ainda não possui histórico de pagamentos"}
							</p>
						</div>
					)}
				</CardContent>
			</Card>

			{/* Help Section */}
			<div className="mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
				<div className="flex items-start gap-3">
					<AlertCircle className="w-5 h-5 text-blue-600 mt-0.5" />
					<div>
						<h4 className="font-semibold text-blue-900 mb-2">
							Dúvidas sobre pagamentos?
						</h4>
						<p className="text-sm text-blue-700 mb-3">
							Nossa equipe financeira está disponível para esclarecer questões sobre cobranças, reembolsos e métodos de pagamento.
						</p>
						<div className="flex gap-2">
							<Button size="sm" variant="outline" className="border-blue-300 text-blue-700">
								Falar com Suporte
							</Button>
							<Button size="sm" variant="outline" className="border-blue-300 text-blue-700">
								Central de Ajuda
							</Button>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
}
