"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { <PERSON><PERSON> } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@ui/components/avatar";
import {
	Dialog,
	DialogContent,
	DialogHeader,
	DialogTitle,
	DialogFooter
} from "@ui/components/dialog";
import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
} from "@ui/components/alert-dialog";
import {
	Calendar,
	Clock,
	Search,
	Filter,
	MapPin,
	Star,
	ChevronLeft,
	ChevronRight,
	Plus,
	Stethoscope,
	Loader2,
	CreditCard
} from "lucide-react";
import { User } from "@prisma/client";
import { format, addDays, startOfWeek, addWeeks, subWeeks } from "date-fns";
import { ptBR } from "date-fns/locale";
import { DoctorC<PERSON> } from "./doctor-card";
import { TimeSlotPicker } from "./time-slot-picker";
import { ConfirmationModal } from "./confirmation-modal";
import { PatientScheduleService, type DoctorAvailable, type Specialty } from "../../services/patient-schedule.service";
import { SubscriptionStatus } from "../../../../../../components/subscription-status";
import { PageHeader } from "../../components/ui/page-header";


interface SchedulePatientClientProps {
	user: User;
}

export function SchedulePatientClient({ user }: SchedulePatientClientProps) {
	const [selectedSpecialty, setSelectedSpecialty] = useState<string>("");
	const [selectedDoctor, setSelectedDoctor] = useState<DoctorAvailable | null>(null);
	const [selectedDate, setSelectedDate] = useState<Date>(new Date());
	const [selectedTime, setSelectedTime] = useState<string>("");
	const [currentWeek, setCurrentWeek] = useState<Date>(startOfWeek(new Date()));
	const [searchTerm, setSearchTerm] = useState("");
	const [debouncedSearch, setDebouncedSearch] = useState("");
	const [isLoading, setIsLoading] = useState(false);
	const [showBookingModal, setShowBookingModal] = useState(false);
	const [subscriptionSummary, setSubscriptionSummary] = useState<{
		hasSubscription: boolean;
		consultationsIncluded: number;
		consultationsUsed: number;
		remainingConsultations: number;
		planName?: string;
	} | null>(null);
	const [isLoadingSubscription, setIsLoadingSubscription] = useState(false);
	const [showSuccessModal, setShowSuccessModal] = useState(false);
	const [showErrorModal, setShowErrorModal] = useState(false);
	const [errorMessage, setErrorMessage] = useState("");
	const [successMessage, setSuccessMessage] = useState("");


	const [specialties, setSpecialties] = useState<Specialty[]>([]);
	const [doctors, setDoctors] = useState<DoctorAvailable[]>([]);
	const [loadingSpecialties, setLoadingSpecialties] = useState(true);
	const [loadingDoctors, setLoadingDoctors] = useState(false);
	const [page, setPage] = useState(1);
	const [pageCount, setPageCount] = useState(1);

	// Instância do serviço
	const scheduleService = new PatientScheduleService();

	// Carregar especialidades ao montar o componente
	useEffect(() => {
		const loadSpecialties = async () => {
			setLoadingSpecialties(true);
			try {
				const specialtiesData = await scheduleService.getSpecialties();
				setSpecialties(specialtiesData);
				// Preselecionar Clínica Geral se disponível
				const clinicaGeral = specialtiesData.find(s =>
					s.name.toLowerCase().includes("clínica geral") ||
					s.name.toLowerCase().includes("clinica geral")
				);
				if (clinicaGeral) {
					setSelectedSpecialty(clinicaGeral.id);
				}
			} catch (error) {
				console.error('Erro ao carregar especialidades:', error);
			} finally {
				setLoadingSpecialties(false);
			}
		};

		loadSpecialties();
	}, []);

	// Debounce do termo de busca para evitar muitas requisições
	useEffect(() => {
		const t = setTimeout(() => setDebouncedSearch(searchTerm.trim()), 300);
		return () => clearTimeout(t);
	}, [searchTerm]);

	// Carregar médicos quando especialidade ou busca mudarem
	useEffect(() => {
		const loadDoctors = async () => {
			setLoadingDoctors(true);
			try {
				const { doctors: docs, pagination } = await scheduleService.getAvailableDoctors(selectedSpecialty || undefined, debouncedSearch || undefined, page);
				setDoctors(docs);
				setPageCount(pagination?.pageCount || 1);
			} catch (error) {
				console.error('Erro ao carregar médicos:', error);
			} finally {
				setLoadingDoctors(false);
			}
		};

		loadDoctors();
	}, [selectedSpecialty, debouncedSearch, page]);

	// Carregar créditos de assinatura
	useEffect(() => {
		const loadSubscriptionSummary = async () => {
			try {
				setIsLoadingSubscription(true);
				const res = await fetch('/api/appointments/subscription');
				if (res.ok) {
					const data = await res.json();
					setSubscriptionSummary(data);
				}
			} catch (e) {
				console.error('Erro ao buscar créditos da assinatura:', e);
			} finally {
				setIsLoadingSubscription(false);
			}
		};
		loadSubscriptionSummary();
	}, []);

	const filteredDoctors = doctors;

	const weekDays = Array.from({ length: 7 }, (_, i) => {
		return addDays(currentWeek, i);
	});

	const handleSelectDoctor = (doctor: DoctorAvailable) => {
		setSelectedDoctor(doctor);
		setSelectedTime("");
		setSelectedDate(new Date());
		setCurrentWeek(startOfWeek(new Date()));
		setShowBookingModal(true);
	};

	const handleSelectTime = (time: string) => {
		setSelectedTime(time);
	};

	const handleConfirmAppointment = async () => {
		if (!selectedDoctor || !selectedDate || !selectedTime) return;

		setIsLoading(true);
		try {
			const scheduledAt = `${format(selectedDate, "yyyy-MM-dd")} ${selectedTime}:00`;

			// Se tem assinatura ativa e créditos, usar subscription
			if (subscriptionSummary?.hasSubscription && subscriptionSummary.remainingConsultations > 0) {
				// Usar a API de subscription com dados simplificados
				const response = await fetch('/api/appointments/subscription', {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json',
					},
					body: JSON.stringify({
						doctorId: selectedDoctor.id,
						scheduledAt: scheduledAt,
						duration: 30,
						consultType: 'VIDEO',
						symptoms: 'Consulta agendada via portal do paciente (assinatura)'
					}),
				});

				if (response.ok) {
					const result = await response.json();
					setSuccessMessage('Consulta agendada com sucesso! Você será redirecionado para suas consultas.');
					setShowSuccessModal(true);
					setShowBookingModal(false);
				} else {
					const error = await response.json();
					setErrorMessage(`Erro ao agendar consulta: ${error.message || 'Tente novamente.'}`);
					setShowErrorModal(true);
				}
			} else {
				// Redirecionar para checkout - URL correta
				window.location.href = `/checkout/${selectedDoctor.id}?scheduledAt=${encodeURIComponent(scheduledAt)}&price=${selectedDoctor.price}`;
			}
		} catch (error) {
			console.error("Erro ao agendar consulta:", error);
			setErrorMessage("Erro ao agendar consulta. Tente novamente.");
			setShowErrorModal(true);
		} finally {
			setIsLoading(false);
		}
	};

	const handleCloseModal = () => {
		setShowBookingModal(false);
		setSelectedDoctor(null);
		setSelectedTime("");
		setSelectedDate(new Date());
	};

	const handleSpecialtyFilter = (specialtyId: string) => {
		setSelectedSpecialty(specialtyId);
		setSelectedDoctor(null);
		setSelectedTime("");
	};

	return (
		<div className="container mx-auto max-w-6xl px-4 py-6">
			<div className="space-y-6">
				<PageHeader
					title="Agendar Consulta"
					subtitle="Escolha o médico e horário ideal para sua consulta"
					showFilter={true}
					filterContent={
						<>
						<div className="hidden lg:block">
							<SubscriptionStatus />
						</div>
							<div>
								<label className="text-sm font-medium mb-2 block">Especialidade</label>
								<select
									value={selectedSpecialty}
									onChange={(e) => handleSpecialtyFilter(e.target.value)}
									className="w-full p-2 border border-gray-300 rounded-md"
								>
									<option value="">Todas as especialidades</option>
									{specialties.map((specialty) => (
										<option key={specialty.id} value={specialty.id}>
											{specialty.name}
										</option>
									))}
								</select>
							</div>
							<div>
								<label className="text-sm font-medium mb-2 block">Buscar</label>
								<input
									type="text"
									placeholder="Buscar médico..."
									value={searchTerm}
									onChange={(e) => setSearchTerm(e.target.value)}
									className="w-full p-2 border border-gray-300 rounded-md"
								/>
							</div>
						</>
					}
				/>

			{/* Lista de Médicos */}
			<div className="space-y-6">
				<div className="flex items-center justify-between">
					<div className="flex items-center gap-2">
						<span className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">{loadingDoctors ? '-' : filteredDoctors.length}</span>
						<h2 className="text-lg font-semibold text-gray-900">Médicos Disponíveis</h2>
						{selectedSpecialty && (
							<Badge className="ml-2 bg-gray-100 text-gray-800 border-gray-200">
								{specialties.find(s => s.id === selectedSpecialty)?.name}
							</Badge>
						)}
					</div>
				</div>

				<div>
					{/* Search (removed standalone filter box as requested; filter lives in header sheet) */}
					<div className="relative mb-6">
						<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
						<input
							type="text"
							placeholder="Buscar médico..."
							value={searchTerm}
							onChange={(e) => setSearchTerm(e.target.value)}
							className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
						/>
					</div>

					{/* Doctors List */}
					{loadingDoctors ? (
						<div className="flex justify-center py-8">
							<div className="animate-pulse text-gray-500">Carregando médicos...</div>
						</div>
					) : (
						<>
							<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
								{filteredDoctors.map(doctor => (
									<DoctorCard
										key={doctor.id}
										doctor={doctor}
										isSelected={selectedDoctor?.id === doctor.id}
										onSelect={() => handleSelectDoctor(doctor)}
									/>
								))}
							</div>

							{filteredDoctors.length === 0 && !loadingDoctors && (
								<div className="text-center py-8">
									<p className="text-gray-500">
										{debouncedSearch ? "Nenhum médico encontrado" : "Nenhum médico disponível"}
									</p>
								</div>
							)}

							{/* Pagination */}
							<div className="flex items-center justify-center gap-2 mt-6">
								<Button variant="outline" size="sm" disabled={page <= 1}
									onClick={() => setPage((p) => Math.max(1, p - 1))}
								>
									Anterior
								</Button>
								<span className="text-sm text-gray-600">Página {page} de {pageCount}</span>
								<Button variant="outline" size="sm" disabled={page >= pageCount}
									onClick={() => setPage((p) => Math.min(pageCount, p + 1))}
								>
									Próxima
								</Button>
							</div>
						</>
					)}
				</div>
			</div>

			{/* Booking Modal */}
			{showBookingModal && selectedDoctor && (
				<Dialog open={showBookingModal} onOpenChange={handleCloseModal}>
					<DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
						<DialogHeader>
							<DialogTitle className="flex items-center gap-3">
								<Avatar className="w-12 h-12">
									<AvatarImage
										src={selectedDoctor.image}
										alt={`Foto de ${selectedDoctor.name}`}
									/>
									<AvatarFallback>
										{selectedDoctor.name.split(' ').map(n => n[0]).join('')}
									</AvatarFallback>
								</Avatar>
								<div>
									<h2 className="text-xl font-semibold">{selectedDoctor.name}</h2>
									<p className="text-sm text-gray-600">{selectedDoctor.specialty}</p>
									<p className="text-xs text-gray-500">{selectedDoctor.crm}</p>
								</div>
							</DialogTitle>
						</DialogHeader>

						<div className="space-y-6">
							{/* Subscription Status */}
							{subscriptionSummary && (
								<div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
									{subscriptionSummary.hasSubscription ? (
										<div className="flex items-center justify-between">
											<div>
												<p className="font-medium text-blue-900">
													{subscriptionSummary.planName || 'Plano Ativo'}
												</p>
												<p className="text-sm text-blue-700">
													Créditos disponíveis: {subscriptionSummary.remainingConsultations} de {subscriptionSummary.consultationsIncluded}
												</p>
											</div>
											<Badge variant={subscriptionSummary.remainingConsultations > 0 ? "default" : "destructive"}>
												{subscriptionSummary.remainingConsultations > 0 ? "Disponível" : "Sem créditos"}
											</Badge>
										</div>
									) : (
										<p className="text-blue-700">
											Assine o ZapVida Sempre para ter consultas mensais incluídas.
										</p>
									)}
								</div>
							)}

							{/* Date Selection */}
							<div>
								<h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
									<Calendar className="w-5 h-5 text-blue-600" />
									Selecionar Data
								</h3>

								{/* Week Navigation */}
								<div className="flex items-center justify-between mb-4">
									<Button
										variant="outline"
										size="sm"
										onClick={() => setCurrentWeek(subWeeks(currentWeek, 1))}
									>
										<ChevronLeft className="h-4 w-4" />
									</Button>
									<h4 className="font-semibold">
										{format(currentWeek, "MMMM yyyy", { locale: ptBR })}
									</h4>
									<Button
										variant="outline"
										size="sm"
										onClick={() => setCurrentWeek(addWeeks(currentWeek, 1))}
									>
										<ChevronRight className="h-4 w-4" />
									</Button>
								</div>

								{/* Calendar */}
								<div className="grid grid-cols-7 gap-2 mb-6">
									{weekDays.map(day => {
										const isToday = format(day, "yyyy-MM-dd") === format(new Date(), "yyyy-MM-dd");
										const isSelected = format(day, "yyyy-MM-dd") === format(selectedDate, "yyyy-MM-dd");
										const isPast = day < new Date();

										return (
											<button
												key={day.toISOString()}
												onClick={() => setSelectedDate(day)}
												disabled={isPast}
												className={`p-3 rounded-lg text-center transition-colors ${
													isPast
														? "text-gray-400 cursor-not-allowed"
														: isSelected
														? "bg-blue-600 text-white"
														: isToday
														? "bg-blue-100 text-blue-600"
														: "hover:bg-gray-100"
												}`}
											>
												<div className="text-xs font-medium">
													{format(day, "EEE", { locale: ptBR })}
												</div>
												<div className="text-lg font-bold">
													{format(day, "dd")}
												</div>
											</button>
										);
									})}
								</div>
							</div>

							{/* Time Selection */}
							<div>
								<h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
									<Clock className="w-5 h-5 text-blue-600" />
									Selecionar Horário
								</h3>
								<TimeSlotPicker
									availableSlots={selectedDoctor.availableSlots}
									selectedTime={selectedTime}
									onSelectTime={handleSelectTime}
								/>
							</div>

							{/* Appointment Summary */}
							{selectedTime && (
								<div className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
									<h4 className="font-semibold mb-3">Resumo da Consulta</h4>
									<div className="space-y-2">
										<div className="flex justify-between">
											<span className="text-gray-600">Data:</span>
											<span className="font-medium">
												{format(selectedDate, "dd/MM/yyyy", { locale: ptBR })}
											</span>
										</div>
										<div className="flex justify-between">
											<span className="text-gray-600">Horário:</span>
											<span className="font-medium">{selectedTime}</span>
										</div>
										<div className="flex justify-between">
											<span className="text-gray-600">Valor:</span>
											<span className="font-bold text-lg">
												{subscriptionSummary?.hasSubscription && subscriptionSummary.remainingConsultations > 0
													? "Incluído na assinatura"
													: `R$ ${selectedDoctor.price}`
												}
											</span>
										</div>
									</div>
								</div>
							)}
						</div>

						<DialogFooter className="gap-3">
							<Button
								variant="outline"
								onClick={handleCloseModal}
								disabled={isLoading}
							>
								Cancelar
							</Button>
							<Button
								onClick={handleConfirmAppointment}
								disabled={!selectedTime || isLoading}
								className="bg-blue-600 hover:bg-blue-700"
							>
								{isLoading ? (
									<>
										<Loader2 className="w-4 h-4 mr-2 animate-spin" />
										Agendando...
									</>
								) : (
									<>
										<Calendar className="w-4 h-4 mr-2" />
										{subscriptionSummary?.hasSubscription && subscriptionSummary.remainingConsultations > 0
											? "Agendar com Assinatura"
											: "Ir para Pagamento"
										}
									</>
								)}
							</Button>
						</DialogFooter>
					</DialogContent>
				</Dialog>
			)}

			{/* Modal de Sucesso */}
			<AlertDialog open={showSuccessModal} onOpenChange={setShowSuccessModal}>
				<AlertDialogContent>
					<AlertDialogHeader>
						<AlertDialogTitle className="flex items-center gap-2 text-green-600">
							<Calendar className="w-5 h-5" />
							Consulta Agendada!
						</AlertDialogTitle>
						<AlertDialogDescription className="text-gray-600">
							{successMessage}
						</AlertDialogDescription>
					</AlertDialogHeader>
					<AlertDialogFooter>
						<AlertDialogAction
							onClick={() => {
								setShowSuccessModal(false);
								window.location.href = '/patient/appointments';
							}}
							className="bg-green-600 hover:bg-green-700"
						>
							Ver Minhas Consultas
						</AlertDialogAction>
					</AlertDialogFooter>
				</AlertDialogContent>
			</AlertDialog>

			{/* Modal de Erro */}
			<AlertDialog open={showErrorModal} onOpenChange={setShowErrorModal}>
				<AlertDialogContent>
					<AlertDialogHeader>
						<AlertDialogTitle className="flex items-center gap-2 text-red-600">
							<Clock className="w-5 h-5" />
							Erro ao Agendar
						</AlertDialogTitle>
						<AlertDialogDescription className="text-gray-600">
							{errorMessage}
						</AlertDialogDescription>
					</AlertDialogHeader>
					<AlertDialogFooter>
						<AlertDialogCancel onClick={() => setShowErrorModal(false)}>
							Fechar
						</AlertDialogCancel>
						<AlertDialogAction
							onClick={() => {
								setShowErrorModal(false);
								setShowBookingModal(true);
							}}
							className="bg-blue-600 hover:bg-blue-700"
						>
							Tentar Novamente
						</AlertDialogAction>
					</AlertDialogFooter>
				</AlertDialogContent>
			</AlertDialog>
			</div>
		</div>
	);
}
