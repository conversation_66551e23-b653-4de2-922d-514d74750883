"use client";

import { <PERSON><PERSON>, AvatarFallback, AvatarImage } from "@ui/components/avatar";
import { Badge } from "@ui/components/badge";
import { Star, MapPin, Clock, Check } from "lucide-react";

interface Doctor {
	id: string;
	name: string;
	specialty: string;
	image?: string;
	crm: string;
	rating: number;
	reviewCount: number;
	price: number;
	location: string;
	availableSlots: string[];
	isOnline: boolean;
}

interface DoctorCardProps {
	doctor: Doctor;
	isSelected: boolean;
	onSelect: () => void;
}

export function DoctorCard({ doctor, isSelected, onSelect }: DoctorCardProps) {
	return (
		<button
			onClick={onSelect}
			className={`w-full p-4 rounded-lg border text-left transition-all duration-200 ${
				isSelected
					? "border-blue-500 bg-blue-50 shadow-md ring-2 ring-blue-200"
					: "border-gray-200 hover:border-gray-300 hover:shadow-sm"
			}`}
		>
			<div className="flex items-start gap-4">
				<div className="relative">
					<Avatar className="w-16 h-16 ring-2 ring-offset-2 ring-gray-100">
						<AvatarImage
							src={doctor.image}
							alt={`Foto de ${doctor.name}`}
							className="object-cover"
						/>
						<AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-600 text-white font-semibold">
							{doctor.name.split(' ').map(n => n[0]).join('')}
						</AvatarFallback>
					</Avatar>
					{doctor.isOnline && (
						<div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 border-2 border-white rounded-full shadow-sm" />
					)}
					{isSelected && (
						<div className="absolute -top-2 -right-2 w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center shadow-lg">
							<Check className="w-3 h-3 text-white" />
						</div>
					)}
				</div>

				<div className="flex-1 min-w-0">
					<div className="flex items-start justify-between mb-2">
						<div className="flex-1">
							<h3 className="font-semibold text-gray-900 mb-1 text-lg">
								{doctor.name}
							</h3>
							<p className="text-sm text-gray-600 mb-1 font-medium">
								{doctor.specialty}
							</p>
							<p className="text-xs text-gray-500">
								{doctor.crm}
							</p>
						</div>
						<div className="text-right">
							<p className="text-xl font-bold text-gray-900">
								R$ {doctor.price}
							</p>
							<p className="text-xs text-gray-500">
								por consulta
							</p>
						</div>
					</div>

					<div className="flex items-center gap-4 mb-3">
						<div className="flex items-center gap-1">
							<Star className="w-4 h-4 text-yellow-400 fill-current" />
							<span className="text-sm font-medium">
								{doctor.rating}
							</span>
							<span className="text-xs text-gray-500">
								({doctor.reviewCount} avaliações)
							</span>
						</div>

						<div className="flex items-center gap-1 text-xs text-gray-500">
							<MapPin className="w-3 h-3" />
							{doctor.location}
						</div>
					</div>

					<div className="flex items-center justify-between">
						<div className="flex items-center gap-1 text-xs text-gray-500">
							<Clock className="w-3 h-3" />
							{doctor.availableSlots.length} horários disponíveis
						</div>

						{doctor.isOnline && (
							<Badge className="bg-green-100 text-green-800 border-green-200">
								Online
							</Badge>
						)}
					</div>
				</div>
			</div>
		</button>
	);
}
