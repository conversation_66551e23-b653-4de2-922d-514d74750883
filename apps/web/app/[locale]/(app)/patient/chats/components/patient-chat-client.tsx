"use client";

import { useState, useEffect } from "react";
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@ui/components/avatar";
import {
	MessageSquare,
	Calendar,
	Clock,
	Video,
	Phone,
	Stethoscope,
	Search,
	Plus
} from "lucide-react";
import { User } from "@prisma/client";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import Link from "next/link";
import { PlantaoLink } from "../../components/plantao-link";

interface PatientChatClientProps {
	user: User;
}

interface ChatAppointment {
	id: string;
	doctor: {
		name: string;
		specialty: string;
		image?: string;
		crm: string;
	};
	scheduledAt: string;
	status: "SCHEDULED" | "IN_PROGRESS" | "COMPLETED" | "CANCELLED";
	type: "REGULAR" | "ON_DUTY";
	urgencyLevel?: "LOW" | "MEDIUM" | "HIGH";
	lastMessage?: {
		content: string;
		createdAt: string;
		isFromDoctor: boolean;
	};
	unreadCount: number;
}

export function PatientChatClient({ user }: PatientChatClientProps) {
	const [appointments, setAppointments] = useState<ChatAppointment[]>([]);
	const [isLoading, setIsLoading] = useState(true);
	const [searchTerm, setSearchTerm] = useState("");

	// Simular dados dos chats (em produção, viria da API)
	useEffect(() => {
		const mockAppointments: ChatAppointment[] = [
			{
				id: "1",
				doctor: {
					name: "Dr. João Silva",
					specialty: "Cardiologia",
					image: "/api/placeholder/40/40",
					crm: "CRM/SP 123456"
				},
				scheduledAt: new Date().toISOString(),
				status: "IN_PROGRESS",
				type: "ON_DUTY",
				urgencyLevel: "MEDIUM",
				lastMessage: {
					content: "Como você está se sentindo agora?",
					createdAt: new Date(Date.now() - 300000).toISOString(), // 5 min atrás
					isFromDoctor: true
				},
				unreadCount: 2
			},
			{
				id: "2",
				doctor: {
					name: "Dra. Maria Santos",
					specialty: "Pediatria",
					image: "/api/placeholder/40/40",
					crm: "CRM/SP 789012"
				},
				scheduledAt: new Date(Date.now() - 86400000).toISOString(), // 1 dia atrás
				status: "COMPLETED",
				type: "REGULAR",
				lastMessage: {
					content: "Qualquer dúvida, estou à disposição.",
					createdAt: new Date(Date.now() - 86400000).toISOString(),
					isFromDoctor: true
				},
				unreadCount: 0
			},
			{
				id: "3",
				doctor: {
					name: "Dr. Carlos Oliveira",
					specialty: "Dermatologia",
					image: "/api/placeholder/40/40",
					crm: "CRM/SP 345678"
				},
				scheduledAt: new Date(Date.now() + 3600000).toISOString(), // em 1 hora
				status: "SCHEDULED",
				type: "REGULAR",
				lastMessage: {
					content: "Consulta agendada para hoje às 15:00",
					createdAt: new Date(Date.now() - 7200000).toISOString(),
					isFromDoctor: false
				},
				unreadCount: 0
			}
		];

		setTimeout(() => {
			setAppointments(mockAppointments);
			setIsLoading(false);
		}, 1000);
	}, []);

	const getStatusBadge = (status: string, type: string, urgencyLevel?: string) => {
		if (type === "ON_DUTY") {
			switch (urgencyLevel) {
				case "HIGH":
					return <Badge variant="destructive">Plantão - Urgente</Badge>;
				case "MEDIUM":
					return <Badge className="bg-orange-100 text-orange-800">Plantão - Moderado</Badge>;
				case "LOW":
					return <Badge className="bg-yellow-100 text-yellow-800">Plantão - Leve</Badge>;
				default:
					return <Badge className="bg-red-100 text-red-800">Plantão 24h</Badge>;
			}
		}

		switch (status) {
			case "IN_PROGRESS":
				return <Badge className="bg-green-100 text-green-800">Ativo</Badge>;
			case "SCHEDULED":
				return <Badge className="bg-blue-100 text-blue-800">Agendado</Badge>;
			case "COMPLETED":
				return <Badge variant="secondary">Finalizado</Badge>;
			case "CANCELLED":
				return <Badge variant="destructive">Cancelado</Badge>;
			default:
				return null;
		}
	};

	const formatLastMessageTime = (dateString: string) => {
		const date = new Date(dateString);
		const now = new Date();
		const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

		if (diffInMinutes < 1) return "agora";
		if (diffInMinutes < 60) return `${diffInMinutes}m`;
		if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h`;
		return format(date, "dd/MM", { locale: ptBR });
	};

	const filteredAppointments = appointments.filter(appointment =>
		appointment.doctor.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
		appointment.doctor.specialty.toLowerCase().includes(searchTerm.toLowerCase())
	);

	const activeChats = filteredAppointments.filter(apt => apt.status === "IN_PROGRESS");
	const scheduledChats = filteredAppointments.filter(apt => apt.status === "SCHEDULED");
	const completedChats = filteredAppointments.filter(apt => apt.status === "COMPLETED");

	if (isLoading) {
		return (
			<div className="container mx-auto py-6 px-4">
				<div className="space-y-4">
					{[1, 2, 3].map(i => (
						<Card key={i} className="animate-pulse">
							<CardContent className="p-4">
								<div className="flex items-center gap-3">
									<div className="w-12 h-12 bg-gray-200 rounded-full" />
									<div className="flex-1 space-y-2">
										<div className="h-4 bg-gray-200 rounded w-1/3" />
										<div className="h-3 bg-gray-200 rounded w-1/2" />
									</div>
								</div>
							</CardContent>
						</Card>
					))}
				</div>
			</div>
		);
	}

	return (
		<div className="container mx-auto py-6 px-4">
			{/* Header */}
			<div className="flex items-center justify-between mb-6">
				<div>
					<h1 className="text-2xl font-bold text-gray-900">Conversas</h1>
					<p className="text-gray-600">Seus chats com médicos</p>
				</div>
				<PlantaoLink>
				<Button className="bg-red-600 hover:bg-red-700">
					<Plus className="w-4 h-4 mr-2" />
					Plantão
				</Button>
			</PlantaoLink>
			</div>

			{/* Search */}
			<div className="relative mb-6">
				<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
				<input
					type="text"
					placeholder="Buscar por médico ou especialidade..."
					value={searchTerm}
					onChange={(e) => setSearchTerm(e.target.value)}
					className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
				/>
			</div>

			{/* Active Chats */}
			{activeChats.length > 0 && (
				<div className="mb-8">
					<h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
						<MessageSquare className="w-5 h-5 text-green-600" />
						Conversas Ativas
					</h2>
					<div className="space-y-3">
						{activeChats.map(appointment => (
							<Link key={appointment.id} href={`/patient/zapchat?appointment=${appointment.id}`}>
								<Card className="hover:shadow-md transition-shadow cursor-pointer border-l-4 border-l-green-500">
									<CardContent className="p-4">
										<div className="flex items-center justify-between">
											<div className="flex items-center gap-3 flex-1">
												<div className="relative">
													<Avatar className="w-12 h-12">
														<AvatarImage src={appointment.doctor.image} />
														<AvatarFallback>
															{appointment.doctor.name.split(' ').map(n => n[0]).join('')}
														</AvatarFallback>
													</Avatar>
													<div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 border-2 border-white rounded-full" />
												</div>
												<div className="flex-1 min-w-0">
													<div className="flex items-center gap-2 mb-1">
														<h3 className="font-semibold text-gray-900 truncate">
															{appointment.doctor.name}
														</h3>
														{getStatusBadge(appointment.status, appointment.type, appointment.urgencyLevel)}
													</div>
													<p className="text-sm text-gray-600 mb-1">
														{appointment.doctor.specialty}
													</p>
													{appointment.lastMessage && (
														<p className="text-sm text-gray-500 truncate">
															{appointment.lastMessage.isFromDoctor ? "💬 " : "Você: "}
															{appointment.lastMessage.content}
														</p>
													)}
												</div>
											</div>
											<div className="flex flex-col items-end gap-1">
												{appointment.lastMessage && (
													<span className="text-xs text-gray-400">
														{formatLastMessageTime(appointment.lastMessage.createdAt)}
													</span>
												)}
												{appointment.unreadCount > 0 && (
													<Badge variant="destructive" className="text-xs px-2 py-1">
														{appointment.unreadCount}
													</Badge>
												)}
												<div className="flex gap-1 mt-1">
													<Button size="sm" variant="ghost" className="p-1 h-8 w-8">
														<Video className="w-4 h-4" />
													</Button>
													<Button size="sm" variant="ghost" className="p-1 h-8 w-8">
														<Phone className="w-4 h-4" />
													</Button>
												</div>
											</div>
										</div>
									</CardContent>
								</Card>
							</Link>
						))}
					</div>
				</div>
			)}

			{/* Scheduled Chats */}
			{scheduledChats.length > 0 && (
				<div className="mb-8">
					<h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
						<Calendar className="w-5 h-5 text-blue-600" />
						Consultas Agendadas
					</h2>
					<div className="space-y-3">
						{scheduledChats.map(appointment => (
							<Card key={appointment.id} className="border-l-4 border-l-blue-500">
								<CardContent className="p-4">
									<div className="flex items-center justify-between">
										<div className="flex items-center gap-3 flex-1">
											<Avatar className="w-12 h-12">
												<AvatarImage src={appointment.doctor.image} />
												<AvatarFallback>
													{appointment.doctor.name.split(' ').map(n => n[0]).join('')}
												</AvatarFallback>
											</Avatar>
											<div className="flex-1">
												<div className="flex items-center gap-2 mb-1">
													<h3 className="font-semibold text-gray-900">
														{appointment.doctor.name}
													</h3>
													{getStatusBadge(appointment.status, appointment.type)}
												</div>
												<p className="text-sm text-gray-600 mb-1">
													{appointment.doctor.specialty}
												</p>
												<p className="text-sm text-blue-600 flex items-center gap-1">
													<Clock className="w-3 h-3" />
													{format(new Date(appointment.scheduledAt), "dd/MM 'às' HH:mm", { locale: ptBR })}
												</p>
											</div>
										</div>
										<div className="flex flex-col gap-2">
											<Button size="sm" variant="outline">
												<MessageSquare className="w-4 h-4 mr-1" />
												Mensagem
											</Button>
										</div>
									</div>
								</CardContent>
							</Card>
						))}
					</div>
				</div>
			)}

			{/* Completed Chats */}
			{completedChats.length > 0 && (
				<div className="mb-8">
					<h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
						<Stethoscope className="w-5 h-5 text-gray-600" />
						Consultas Anteriores
					</h2>
					<div className="space-y-3">
						{completedChats.map(appointment => (
							<Link key={appointment.id} href={`/patient/zapchat?appointment=${appointment.id}`}>
								<Card className="hover:shadow-md transition-shadow cursor-pointer opacity-75 hover:opacity-100">
									<CardContent className="p-4">
										<div className="flex items-center justify-between">
											<div className="flex items-center gap-3 flex-1">
												<Avatar className="w-12 h-12">
													<AvatarImage src={appointment.doctor.image} />
													<AvatarFallback>
														{appointment.doctor.name.split(' ').map(n => n[0]).join('')}
													</AvatarFallback>
												</Avatar>
												<div className="flex-1">
													<div className="flex items-center gap-2 mb-1">
														<h3 className="font-semibold text-gray-900">
															{appointment.doctor.name}
														</h3>
														{getStatusBadge(appointment.status, appointment.type)}
													</div>
													<p className="text-sm text-gray-600 mb-1">
														{appointment.doctor.specialty}
													</p>
													<p className="text-sm text-gray-500">
														{format(new Date(appointment.scheduledAt), "dd/MM/yyyy", { locale: ptBR })}
													</p>
												</div>
											</div>
											<div>
												<Button size="sm" variant="ghost">
													<MessageSquare className="w-4 h-4 mr-1" />
													Ver Chat
												</Button>
											</div>
										</div>
									</CardContent>
								</Card>
							</Link>
						))}
					</div>
				</div>
			)}

			{/* Empty State */}
			{filteredAppointments.length === 0 && !isLoading && (
				<div className="text-center py-12">
					<div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
						<MessageSquare className="w-8 h-8 text-gray-400" />
					</div>
					<h3 className="text-lg font-semibold text-gray-900 mb-2">
						Nenhuma conversa encontrada
					</h3>
					<p className="text-gray-600 mb-6">
						{searchTerm ? "Tente buscar por outro termo" : "Inicie uma consulta para começar a conversar com médicos"}
					</p>
					<Link href="/pay/plantao">
						<Button className="bg-red-600 hover:bg-red-700">
							<Stethoscope className="w-4 h-4 mr-2" />
							Solicitar Plantão Médico
						</Button>
					</Link>
				</div>
			)}
		</div>
	);
}
