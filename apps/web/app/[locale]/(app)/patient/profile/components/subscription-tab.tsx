'use client';

import { useState, useEffect } from "react";
import { But<PERSON> } from "@ui/components/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { Badge } from "@ui/components/badge";
import { useToast } from "@ui/hooks/use-toast";
import {
	CreditCard,
	Calendar,
	CheckCircle,
	XCircle,
	Clock,
	AlertTriangle,
	Zap,
	Users,
	Heart,
	Loader2,
	Crown
} from "lucide-react";

import type { User as UserType } from "database";
import { formatCurrency } from "@lib/utils";

interface SubscriptionTabProps {
	user: UserType;
}

interface Subscription {
	id: string;
	planName: string;
	planPrice: number;
	status: string;
	cycle: string;
	startDate: string;
	nextBillingDate: string;
	consultationsIncluded: number;
	consultationsUsed: number;
	features: string[];
}

const PLAN_ICONS = {
	"zapvida-sempre": Crown,
} as const;

const PLAN_COLORS = {
	"zapvida-sempre": "bg-purple-50 text-purple-600",
} as const;

export function SubscriptionTab({ user }: SubscriptionTabProps) {
	const [subscription, setSubscription] = useState<Subscription | null>(null);
	const [isLoading, setIsLoading] = useState(true);
	const [isChangingPlan, setIsChangingPlan] = useState(false);
	const { toast } = useToast();

	useEffect(() => {
		const loadSubscription = async () => {
			try {
				const response = await fetch('/api/patient/subscription/details');
				if (response.ok) {
					const data = await response.json();
					if (data.subscription) {
						setSubscription(data.subscription);
					}
				}
			} catch (error) {
				console.error('Erro ao carregar assinatura:', error);
			} finally {
				setIsLoading(false);
			}
		};

		loadSubscription();
	}, []);

	const getStatusBadge = (status: string) => {
		switch (status) {
			case 'ACTIVE':
				return <Badge className="bg-green-100 text-green-800"><CheckCircle className="w-3 h-3 mr-1" />Ativa</Badge>;
			case 'PAUSED':
				return <Badge className="bg-yellow-100 text-yellow-800"><Clock className="w-3 h-3 mr-1" />Pausada</Badge>;
			case 'CANCELLED':
				return <Badge className="bg-red-100 text-red-800"><XCircle className="w-3 h-3 mr-1" />Cancelada</Badge>;
			case 'OVERDUE':
				return <Badge className="bg-orange-100 text-orange-800"><AlertTriangle className="w-3 h-3 mr-1" />Vencida</Badge>;
			default:
				return <Badge variant="secondary">Desconhecido</Badge>;
		}
	};

	const getPlanIcon = (planId: string) => {
		const iconKey = planId as keyof typeof PLAN_ICONS;
		const Icon = PLAN_ICONS[iconKey] || Heart;
		const colorKey = planId as keyof typeof PLAN_COLORS;
		const color = PLAN_COLORS[colorKey] || "bg-gray-50 text-gray-600";
		return <Icon className={`w-6 h-6 p-1 rounded ${color}`} />;
	};

	const handleCancelSubscription = async () => {
		if (!subscription) return;

		setIsChangingPlan(true);
		try {
			const response = await fetch(`/api/patient/subscription/${subscription.id}/cancel`, {
				method: 'POST',
			});

			if (response.ok) {
				setSubscription(prev => prev ? { ...prev, status: 'CANCELLED' } : null);
				toast({
					title: "Assinatura cancelada",
					description: "Sua assinatura foi cancelada com sucesso.",
				});
			} else {
				throw new Error('Falha ao cancelar assinatura');
			}
		} catch (error) {
			toast({
				title: "Erro",
				description: "Não foi possível cancelar sua assinatura. Tente novamente.",
				variant: "destructive",
			});
		} finally {
			setIsChangingPlan(false);
		}
	};

	const handleUpgradePlan = () => {
		// Redirecionar para página de planos
		window.location.href = '/patient/subscriptions';
	};

	if (isLoading) {
		return (
			<div className="flex items-center justify-center p-8">
				<Loader2 className="w-8 h-8 animate-spin" />
			</div>
		);
	}

	if (!subscription) {
		return (
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<CreditCard className="w-5 h-5" />
						Nenhuma Assinatura Ativa
					</CardTitle>
					<CardDescription>
						Você não possui uma assinatura ativa. Assine o plano ZapVida Sempre para ter acesso completo aos nossos serviços.
					</CardDescription>
				</CardHeader>
				<CardContent>
					<div className="text-center py-8">
						<Heart className="w-16 h-16 mx-auto text-gray-300 mb-4" />
						<h3 className="text-lg font-semibold mb-2">Aproveite nossos planos de saúde</h3>
						<p className="text-muted-foreground mb-6">
							Tenha acesso a 2 consultas médicas por mês, plantão médico 24h e muito mais.
						</p>
						<div className="flex justify-center">
							<Button onClick={handleUpgradePlan}>
								Ver Planos Disponíveis
							</Button>
						</div>
					</div>
				</CardContent>
			</Card>
		);
	}

	const consultationProgress = (subscription.consultationsUsed / subscription.consultationsIncluded) * 100;

	return (
		<div className="space-y-4">
			{/* Plano e Status */}
			<div className="flex items-center justify-between p-4 bg-slate-50 rounded-lg">
				<div className="flex items-center gap-3">
					{getPlanIcon(subscription.id)}
					<div>
						<h3 className="font-semibold text-slate-900">{subscription.planName}</h3>
						<p className="text-sm text-slate-600">
							{formatCurrency(subscription.planPrice)}/{subscription.cycle === 'MONTHLY' ? 'mês' : 'ano'}
						</p>
					</div>
				</div>
				{getStatusBadge(subscription.status)}
			</div>

			{/* Uso de Consultas */}
			<div className="space-y-3">
				<div className="flex items-center justify-between">
					<span className="text-sm font-medium text-slate-700">Consultas utilizadas</span>
					<span className="text-sm text-slate-600">
						{subscription.consultationsUsed} de {subscription.consultationsIncluded}
					</span>
				</div>
				<div className="w-full bg-slate-200 rounded-full h-2">
					<div
						className="bg-blue-600 h-2 rounded-full transition-all duration-300"
						style={{ width: `${Math.min(consultationProgress, 100)}%` }}
					/>
				</div>
				{subscription.consultationsUsed >= subscription.consultationsIncluded && (
					<p className="text-sm text-orange-600">
						⚠️ Você atingiu o limite de consultas deste mês.
					</p>
				)}
			</div>

			{/* Informações de Cobrança */}
			<div className="grid grid-cols-2 gap-4 text-sm">
				<div>
					<span className="text-slate-500">Início:</span>
					<p className="font-medium">{new Date(subscription.startDate).toLocaleDateString('pt-BR')}</p>
				</div>
				<div>
					<span className="text-slate-500">Próxima cobrança:</span>
					<p className="font-medium">
						{subscription.nextBillingDate ? new Date(subscription.nextBillingDate).toLocaleDateString('pt-BR') : 'N/A'}
					</p>
				</div>
			</div>

			{/* Features do Plano */}
			<div className="space-y-3">
				<h4 className="font-medium text-slate-700">Benefícios inclusos:</h4>
				<ul className="space-y-2">
					{(subscription.features || []).map((feature, index) => (
						<li key={index} className="flex items-center gap-2 text-sm text-slate-600">
							<CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
							{feature}
						</li>
					))}
				</ul>
			</div>

			{/* Ações */}
			<div className="flex gap-3 pt-4 border-t">
				<Button onClick={handleUpgradePlan} variant="outline" size="sm" className="flex-1">
					Alterar Plano
				</Button>
				{subscription.status === 'ACTIVE' && (
					<Button
						onClick={handleCancelSubscription}
						variant="destructive"
						size="sm"
						disabled={isChangingPlan}
						className="flex-1"
					>
						{isChangingPlan && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
						Cancelar
					</Button>
				)}
			</div>

		</div>
	);
}
