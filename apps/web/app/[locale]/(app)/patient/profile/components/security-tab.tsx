'use client';

import { useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { Button } from "@ui/components/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@ui/components/form";
import { Input } from "@ui/components/input";
import { useToast } from "@ui/hooks/use-toast";
import {
	Shield,
	Lock,
	Eye,
	EyeOff,
	Loader2,
	AlertTriangle,
	X
} from "lucide-react";
import type { User as UserType } from "database";

const passwordSchema = z.object({
	currentPassword: z.string().min(1, "Senha atual é obrigatória"),
	newPassword: z.string().min(8, "Nova senha deve ter pelo menos 8 caracteres"),
	confirmPassword: z.string().min(1, "Confirmação é obrigatória"),
}).refine((data) => data.newPassword === data.confirmPassword, {
	message: "Senhas não conferem",
	path: ["confirmPassword"],
});

type PasswordFormValues = z.infer<typeof passwordSchema>;

interface SecurityTabProps {
	user: UserType;
}


export function SecurityTab({ user }: SecurityTabProps) {
	const [showCurrentPassword, setShowCurrentPassword] = useState(false);
	const [showNewPassword, setShowNewPassword] = useState(false);
	const [showConfirmPassword, setShowConfirmPassword] = useState(false);
	const [isChangingPassword, setIsChangingPassword] = useState(false);
	const { toast } = useToast();

	const passwordForm = useForm<PasswordFormValues>({
		resolver: zodResolver(passwordSchema),
		defaultValues: {
			currentPassword: "",
			newPassword: "",
			confirmPassword: "",
		},
	});

	const onPasswordSubmit = async (data: PasswordFormValues) => {
		setIsChangingPassword(true);
		try {
			const response = await fetch('/api/auth/change-password', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({
					currentPassword: data.currentPassword,
					newPassword: data.newPassword,
				}),
			});

			if (response.ok) {
				toast({
					title: "Senha alterada",
					description: "Sua senha foi alterada com sucesso.",
				});
				passwordForm.reset();
			} else {
				const error = await response.json();
				throw new Error(error.message || 'Erro ao alterar senha');
			}
		} catch (error) {
			toast({
				title: "Erro",
				description: error instanceof Error ? error.message : "Não foi possível alterar sua senha.",
				variant: "destructive",
			});
		} finally {
			setIsChangingPassword(false);
		}
	};


	return (
		<div className="space-y-6">
			{/* Alterar Senha */}
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<Lock className="w-5 h-5" />
						Alterar Senha
					</CardTitle>
					<CardDescription>
						Mantenha sua conta segura com uma senha forte.
					</CardDescription>
				</CardHeader>
				<CardContent>
					<Form {...passwordForm}>
						<form onSubmit={passwordForm.handleSubmit(onPasswordSubmit)} className="space-y-4">
							<FormField
								control={passwordForm.control}
								name="currentPassword"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Senha Atual</FormLabel>
										<FormControl>
											<div className="relative">
												<Input
													type={showCurrentPassword ? "text" : "password"}
													{...field}
												/>
												<Button
													type="button"
													variant="ghost"
													size="sm"
													className="absolute right-0 top-0 h-full px-3"
													onClick={() => setShowCurrentPassword(!showCurrentPassword)}
												>
													{showCurrentPassword ? (
														<EyeOff className="w-4 h-4" />
													) : (
														<Eye className="w-4 h-4" />
													)}
												</Button>
											</div>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							<FormField
								control={passwordForm.control}
								name="newPassword"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Nova Senha</FormLabel>
										<FormControl>
											<div className="relative">
												<Input
													type={showNewPassword ? "text" : "password"}
													{...field}
												/>
												<Button
													type="button"
													variant="ghost"
													size="sm"
													className="absolute right-0 top-0 h-full px-3"
													onClick={() => setShowNewPassword(!showNewPassword)}
												>
													{showNewPassword ? (
														<EyeOff className="w-4 h-4" />
													) : (
														<Eye className="w-4 h-4" />
													)}
												</Button>
											</div>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							<FormField
								control={passwordForm.control}
								name="confirmPassword"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Confirmar Nova Senha</FormLabel>
										<FormControl>
											<div className="relative">
												<Input
													type={showConfirmPassword ? "text" : "password"}
													{...field}
												/>
												<Button
													type="button"
													variant="ghost"
													size="sm"
													className="absolute right-0 top-0 h-full px-3"
													onClick={() => setShowConfirmPassword(!showConfirmPassword)}
												>
													{showConfirmPassword ? (
														<EyeOff className="w-4 h-4" />
													) : (
														<Eye className="w-4 h-4" />
													)}
												</Button>
											</div>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							<Button type="submit" disabled={isChangingPassword}>
								{isChangingPassword && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
								Alterar Senha
							</Button>
						</form>
					</Form>
				</CardContent>
			</Card>


			{/* Excluir Conta */}
			<Card className="border-red-200">
				<CardHeader>
					<CardTitle className="flex items-center gap-2 text-red-600">
						<AlertTriangle className="w-5 h-5" />
						Zona de Perigo
					</CardTitle>
					<CardDescription>
						Ações irreversíveis relacionadas à sua conta.
					</CardDescription>
				</CardHeader>
				<CardContent>
					<div className="space-y-4">
						<div className="p-4 border border-red-200 rounded-lg bg-red-50">
							<h4 className="font-medium text-red-800 mb-2">Excluir conta</h4>
							<p className="text-sm text-red-700 mb-4">
								Esta ação excluirá permanentemente sua conta e todos os dados associados.
								Esta ação não pode ser desfeita.
							</p>
							<Button variant="destructive" size="sm">
								<X className="w-4 h-4 mr-2" />
								Solicitar exclusão da conta
							</Button>
						</div>
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
