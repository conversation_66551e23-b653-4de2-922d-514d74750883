'use client';

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@ui/components/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { Badge } from "@ui/components/badge";
import { UserAvatar } from "@shared/components/UserAvatar";
import { UserAvatarUpload } from "@saas/settings/components/UserAvatarUpload";
import { useToast } from "@ui/hooks/use-toast";
import {
	User,
	CreditCard,
	FileText,
	Settings,
	Calendar,
	Heart,
	Shield,
	Bell,
	MapPin,
	Phone,
	Mail,
	Stethoscope,
	Activity
} from "lucide-react";
import type { User as UserType } from "database";
import { PersonalInfoTab } from "./personal-info-tab";
import { SubscriptionTab } from "./subscription-tab";
import { SecurityTab } from "./security-tab";

interface PatientProfileClientProps {
	user: UserType;
}

interface QuickStats {
	appointmentsThisMonth: number;
	subscriptionStatus: string;
	lastAppointment: string | null;
	upcomingAppointments: number;
}

export function PatientProfileClient({ user }: PatientProfileClientProps) {
	const [quickStats, setQuickStats] = useState<QuickStats | null>(null);
	const [isLoading, setIsLoading] = useState(true);
	const router = useRouter();
	const { toast } = useToast();

	// Carregar estatísticas rápidas do usuário
	useEffect(() => {
		const loadQuickStats = async () => {
			try {
				const response = await fetch('/api/patient/profile/stats');
				if (response.ok) {
					const stats = await response.json();
					setQuickStats(stats);
				}
			} catch (error) {
				console.error('Erro ao carregar estatísticas:', error);
			} finally {
				setIsLoading(false);
			}
		};

		loadQuickStats();
	}, []);

	const quickActions = [
		{
			title: "Agendar Consulta",
			description: "Nova consulta médica",
			icon: <Calendar className="w-5 h-5" />,
			href: "/patient/schedule",
			color: "bg-blue-50 text-blue-600 hover:bg-blue-100 border-blue-200"
		},
		{
			title: "Plantão 24h",
			description: "Atendimento imediato",
			icon: <Stethoscope className="w-5 h-5" />,
			href: "/pay/plantao",
			color: "bg-green-50 text-green-600 hover:bg-green-100 border-green-200"
		},
		{
			title: "Minhas Consultas",
			description: "Histórico de consultas",
			icon: <FileText className="w-5 h-5" />,
			href: "/patient/appointments",
			color: "bg-purple-50 text-purple-600 hover:bg-purple-100 border-purple-200"
		},
		{
			title: "ZapChat",
			description: "Chat com médicos",
			icon: <Activity className="w-5 h-5" />,
			href: "/patient/zapchat",
			color: "bg-orange-50 text-orange-600 hover:bg-orange-100 border-orange-200"
		}
	];

	return (
		<div className="container max-w-6xl mx-auto p-6 space-y-6">
			{/* Header do Perfil - Simplificado */}
			<div className="bg-white rounded-lg border p-6 shadow-sm">
				<div className="flex items-center gap-4">
					<div className="relative">
						<UserAvatarUpload
							onSuccess={() => {
								toast({
									title: "Foto atualizada",
									description: "Sua foto de perfil foi atualizada com sucesso.",
								});
							}}
							onError={() => {
								toast({
									title: "Erro",
									description: "Não foi possível atualizar sua foto de perfil.",
									variant: "error",
								});
							}}
						/>
					</div>

					<div className="flex-1">
						<h1 className="text-2xl font-bold text-slate-900 mb-1">{user.name}</h1>
						<p className="text-slate-600 mb-3">{user.email}</p>

						<div className="flex flex-wrap gap-2">
							<Badge variant="outline" className="bg-slate-50 border-slate-200">
								Paciente
							</Badge>
							{quickStats?.subscriptionStatus === "ACTIVE" && (
								<Badge className="bg-green-100 text-green-700 border-green-200">
									<Heart className="w-3 h-3 mr-1" />
									Assinante Ativo
								</Badge>
							)}
						</div>
					</div>

					{/* Stats - Simplificados */}
					{!isLoading && quickStats && (
						<div className="flex gap-6 text-center">
							<div>
								<div className="text-2xl font-bold text-slate-900">{quickStats.appointmentsThisMonth}</div>
								<div className="text-xs text-slate-500">Consultas este mês</div>
							</div>
							<div>
								<div className="text-2xl font-bold text-slate-900">{quickStats.upcomingAppointments}</div>
								<div className="text-xs text-slate-500">Próximas</div>
							</div>
						</div>
					)}
				</div>
			</div>

			{/* Ações Rápidas - Melhoradas */}
			<div className="grid grid-cols-2 md:grid-cols-4 gap-4">
				{quickActions.map((action, index) => (
					<Card
						key={index}
						className="cursor-pointer transition-all hover:shadow-md hover:scale-105 group border-2 hover:border-opacity-50"
						onClick={() => router.push(action.href)}
					>
						<CardContent className="p-6 text-center">
							<div className={`inline-flex p-3 rounded-xl mb-4 ${action.color} group-hover:scale-110 transition-transform`}>
								{action.icon}
							</div>
							<h3 className="font-semibold mb-2 text-slate-900">{action.title}</h3>
							<p className="text-sm text-slate-600">{action.description}</p>
						</CardContent>
					</Card>
				))}
			</div>

			{/* Configurações do Perfil - Simplificadas */}
			<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
				{/* Informações Pessoais */}
				<Card>
					<CardHeader className="pb-4">
						<CardTitle className="flex items-center gap-2 text-lg">
							<User className="w-5 h-5 text-slate-600" />
							Informações Pessoais
						</CardTitle>
					</CardHeader>
					<CardContent>
						<PersonalInfoTab user={user} />
					</CardContent>
				</Card>

				{/* Assinatura */}
				<Card>
					<CardHeader className="pb-4">
						<CardTitle className="flex items-center gap-2 text-lg">
							<CreditCard className="w-5 h-5 text-slate-600" />
							Minha Assinatura
						</CardTitle>
					</CardHeader>
					<CardContent>
						<SubscriptionTab user={user} />
					</CardContent>
				</Card>
			</div>

			{/* Segurança - Card separado */}
			<Card>
				<CardHeader className="pb-4">
					<CardTitle className="flex items-center gap-2 text-lg">
						<Shield className="w-5 h-5 text-slate-600" />
						Segurança da Conta
					</CardTitle>
					<CardDescription>
						Gerencie senha e configurações de segurança
					</CardDescription>
				</CardHeader>
				<CardContent>
					<SecurityTab user={user} />
				</CardContent>
			</Card>
		</div>
	);
}
