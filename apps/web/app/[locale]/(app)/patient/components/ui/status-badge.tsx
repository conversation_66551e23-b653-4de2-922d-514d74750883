"use client";

import { Badge } from "@ui/components/badge";
import { CheckCircle, Clock, XCircle, AlertCircle } from "lucide-react";
import { cn } from "@ui/lib";

interface StatusBadgeProps {
	status: "ACTIVE" | "COMPLETED" | "CANCELLED" | "EXPIRED" | "PENDING";
	size?: "sm" | "md" | "lg";
	showIcon?: boolean;
	className?: string;
}

const statusConfig = {
	ACTIVE: {
		label: "Ativa",
		variant: "default" as const,
		icon: CheckCircle,
		className: "bg-green-100 text-green-800 border-green-200"
	},
	COMPLETED: {
		label: "Concluída",
		variant: "secondary" as const,
		icon: CheckCircle,
		className: "bg-gray-100 text-gray-800 border-gray-200"
	},
	CANCELLED: {
		label: "Cancelada",
		variant: "destructive" as const,
		icon: XCircle,
		className: "bg-red-100 text-red-800 border-red-200"
	},
	EXPIRED: {
		label: "Expirada",
		variant: "outline" as const,
		icon: AlertCircle,
		className: "bg-orange-100 text-orange-800 border-orange-200"
	},
	PENDING: {
		label: "Pendente",
		variant: "outline" as const,
		icon: Clock,
		className: "bg-yellow-100 text-yellow-800 border-yellow-200"
	},
	IN_PROGRESS: {
		label: "Em andamento",
		variant: "default" as const,
		icon: CheckCircle,
		className: "bg-blue-100 text-blue-800 border-blue-200"
	}
};

const sizeConfig = {
	sm: {
		badge: "text-xs px-2 py-1",
		icon: "w-3 h-3"
	},
	md: {
		badge: "text-sm px-3 py-1",
		icon: "w-4 h-4"
	},
	lg: {
		badge: "text-base px-4 py-2",
		icon: "w-5 h-5"
	}
};

export function StatusBadge({
	status,
	size = "md",
	showIcon = true,
	className
}: StatusBadgeProps) {
	const config = statusConfig[status];
	const sizeStyles = sizeConfig[size];

	// Fallback para status inválido
	if (!config) {
		return (
			<Badge
				className={cn(
					"inline-flex items-center gap-1.5 font-medium border",
					"bg-gray-100 text-gray-800 border-gray-200",
					sizeStyles.badge,
					className
				)}
			>
				{showIcon && <AlertCircle className={sizeStyles.icon} />}
				{status || "Desconhecido"}
			</Badge>
		);
	}

	const Icon = config.icon;

	return (
		<Badge
			className={cn(
				"inline-flex items-center gap-1.5 font-medium border",
				config.className,
				sizeStyles.badge,
				className
			)}
		>
			{showIcon && <Icon className={sizeStyles.icon} />}
			{config.label}
		</Badge>
	);
}
