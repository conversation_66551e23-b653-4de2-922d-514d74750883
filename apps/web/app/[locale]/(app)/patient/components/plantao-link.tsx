'use client';

import { useRouter } from 'next/navigation';
import { ReactNode, useState } from 'react';

interface PlantaoLinkProps {
  children: ReactNode;
  className?: string;
  urgencyLevel?: string;
}

export function PlantaoLink({ children, className, urgencyLevel = 'medium' }: PlantaoLinkProps) {
  const router = useRouter();
  const [isChecking, setIsChecking] = useState(false);

  const handleClick = async (e: React.MouseEvent) => {
    e.preventDefault();

    if (isChecking) return;

    setIsChecking(true);

    try {
      // Sempre redirecionar para a página de pagamento do plantão
      router.push(`/pay/plantao?urgencyLevel=${urgencyLevel}`);
    } catch (error) {
      console.error('Erro ao redirecionar para plantão:', error);
      router.push(`/pay/plantao?urgencyLevel=${urgencyLevel}`);
    } finally {
      setIsChecking(false);
    }
  };

  return (
    <a
      href="#"
      onClick={handleClick}
      className={className}
      style={{ cursor: isChecking ? 'wait' : 'pointer' }}
    >
      {children}
    </a>
  );
}
