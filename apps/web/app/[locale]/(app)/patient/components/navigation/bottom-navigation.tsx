"use client";

import { cn } from "@ui/lib";
import { Home, Calendar, MessageCircle, User } from "lucide-react";
import Link from "next/link";
import { usePathname } from "next/navigation";


const navigationItems = [
	{
		id: "home",
		label: "In<PERSON>cio",
		icon: Home,
		href: "/patient/dashboard",
		activePatterns: ["/patient/dashboard", "/patient"]
	},
	{
		id: "appointments",
		label: "Consultas",
		icon: Calendar,
		href: "/patient/appointments",
		activePatterns: ["/patient/appointments"]
	},
	{
		id: "zapchat",
		label: "ZapChat",
		icon: MessageCircle,
		href: "/patient/zapchat",
		activePatterns: ["/patient/zapchat", "/patient/chat", "/patient/chats"]
	},
	{
		id: "profile",
		label: "Perfil",
		icon: User,
		href: "/patient/profile",
		activePatterns: ["/patient/profile", "/patient/settings"]
	}
];

export function BottomNavigation() {
	const pathname = usePathname();

	const isActive = (patterns: string[]) => {
		return patterns.some(pattern => pathname.startsWith(pattern));
	};

	return (
		<>
			{/* Spacer para compensar a navegação fixa */}
			<div className="h-20 md:hidden" />

			{/* Navegação inferior fixa */}
			<nav className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 md:hidden z-50">
				<div className="flex items-center justify-around py-2 px-4 safe-area-bottom">
					{navigationItems.map((item) => {
						const Icon = item.icon;
						const active = isActive(item.activePatterns);

						return (
							<Link
								key={item.id}
								href={item.href}
								className={cn(
									"flex flex-col items-center justify-center min-w-0 py-2 px-1 transition-colors",
									"text-xs font-medium",
									active
										? "text-blue-600"
										: "text-gray-500 hover:text-gray-700"
								)}
							>
								<Icon className={cn(
									"w-6 h-6 mb-1",
									active && "text-blue-600"
								)} />
								<span className="truncate leading-none">
									{item.label}
								</span>
							</Link>
						);
					})}
				</div>
			</nav>
		</>
	);
}
