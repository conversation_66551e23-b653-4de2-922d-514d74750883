"use client";

import type { User } from 'database';
import { useState, useEffect } from "react";
import {
	X,
	Home,
	Calendar,
	MessageCircle,
	User2Icon,
	Plus,
	Clock,
	LogOut,
	Settings,
	Bell,
	Heart,
	FileText,
	CreditCard,
	Shield,
	HelpCircle,
	MapPin,
	Star,
	TrendingUp,
	Activity,
	ChevronRight
} from "lucide-react";
import { Button } from "@ui/components/button";
import { Avatar, AvatarFallback, AvatarImage } from "@ui/components/avatar";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@ui/lib";
import { PlantaoLink } from "../plantao-link";

interface PatientSidemenuProps {
	user: User;
	isOpen: boolean;
	onClose: () => void;
}

const navigationItems = [
	{
		id: "dashboard",
		label: "Início",
		icon: Home,
		href: "/patient/dashboard",
		activePatterns: ["/patient/dashboard"],
		description: "Visão geral da sua saúde"
	},
	{
		id: "appointments",
		label: "Consultas",
		icon: Calendar,
		href: "/patient/appointments",
		activePatterns: ["/patient/appointments"],
		description: "Agende e acompanhe consultas"
	},
	{
		id: "zapchat",
		label: "ZapChat",
		icon: MessageCircle,
		href: "/patient/zapchat",
		activePatterns: ["/patient/zapchat", "/patient/chat", "/patient/chats", "/patient/plantao"],
		description: "Chat, vídeo e áudio"
	},

];

const quickActions = [
	{
		id: "schedule",
		label: "Nova Consulta",
		icon: Plus,
		href: "/patient/schedule",
		description: "Agende uma consulta",
		color: "blue"
	},
	{
		id: "plantao",
		label: "Plantão 24h",
		icon: Clock,
		href: "/pay/plantao",
		description: "Atendimento imediato",
		color: "orange",
		isExternal: true
	}
];

const profileActions = [
	{
		id: "profile",
		label: "Meu Perfil",
		icon: User2Icon,
		href: "/patient/profile",
		description: "Editar informações pessoais"
	},
	{
		id: "settings",
		label: "Configurações",
		icon: Settings,
		href: "/patient/settings",
		description: "Preferências e notificações"
	},

];

export function PatientSidemenu({ user, isOpen, onClose }: PatientSidemenuProps) {
	const pathname = usePathname();

	const getUserInitials = (name: string) => {
		return name
			.split(" ")
			.map(n => n[0])
			.join("")
			.toUpperCase()
			.slice(0, 2);
	};

	const isActive = (patterns: string[]) => {
		if (!pathname) return false;
		return patterns.some(pattern => pathname.startsWith(pattern));
	};

	// Fechar sidemenu ao pressionar ESC
	useEffect(() => {
		const handleEscape = (e: KeyboardEvent) => {
			if (e.key === 'Escape') {
				onClose();
			}
		};

		if (isOpen) {
			document.addEventListener('keydown', handleEscape);
			document.body.style.overflow = 'hidden';
		}

		return () => {
			document.removeEventListener('keydown', handleEscape);
			document.body.style.overflow = 'unset';
		};
	}, [isOpen, onClose]);

	// Fechar ao clicar fora
	const handleBackdropClick = (e: React.MouseEvent) => {
		if (e.target === e.currentTarget) {
			onClose();
		}
	};

	const handleItemClick = () => {
		onClose();
	};

	// Adicionar indicador de notificações
	const hasNotifications = true; // Em produção, viria do contexto do usuário

	return (
		<>
			{/* Backdrop com animação */}
			<div
				className={cn(
					"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 transition-all duration-300",
					isOpen ? "opacity-100" : "opacity-0 pointer-events-none"
				)}
				onClick={handleBackdropClick}
			/>

			{/* Sidemenu com animação melhorada */}
			<div
				className={cn(
					"fixed top-0 left-0 h-full w-80 max-w-[85vw] bg-white shadow-2xl z-50 transform transition-all duration-300 ease-out flex flex-col",
					isOpen ? "translate-x-0" : "-translate-x-full"
				)}
			>
				{/* Header do Sidemenu */}
				<div className="flex items-center justify-between p-6 border-b border-gray-100 bg-gradient-to-r from-blue-50 to-indigo-50 flex-shrink-0">
					<div className="flex items-center space-x-3">
						<Avatar className="w-12 h-12 ring-2 ring-white shadow-sm">
							<AvatarImage src={user.avatarUrl || ""} alt={user.name || ""} />
							<AvatarFallback className="text-lg font-semibold bg-gradient-to-br from-blue-500 to-indigo-600 text-white">
								{getUserInitials(user.name || "U")}
							</AvatarFallback>
						</Avatar>
						<div>
							<h3 className="font-semibold text-gray-900">{user.name}</h3>
							<p className="text-sm text-gray-500">{user.email}</p>
						</div>
					</div>
					<Button
						variant="ghost"
						size="sm"
						onClick={onClose}
						className="h-8 w-8 p-0 hover:bg-white/80 transition-colors"
					>
						<X className="w-5 h-5" />
					</Button>
				</div>

				{/* Conteúdo do Sidemenu com scroll */}
				<div className="flex-1 overflow-y-auto overflow-x-hidden scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
					{/* Navegação Principal */}
					<div className="p-4">
						<h4 className="text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3 flex items-center gap-2">
							<span className="w-2 h-2 bg-blue-500 rounded-full"></span>
							Navegação
						</h4>
						<div className="space-y-1">
							{navigationItems.map((item) => {
								const Icon = item.icon;
								const active = isActive(item.activePatterns);

								return (
									<Link
										key={item.id}
										href={item.href}
										onClick={handleItemClick}
										className={cn(
											"flex items-center space-x-3 px-3 py-3 rounded-lg transition-all duration-200 group relative",
											"hover:bg-gray-50 hover:shadow-sm hover:scale-[1.02]",
											active
												? "bg-blue-50 text-blue-700 border border-blue-200 shadow-sm"
												: "text-gray-700 hover:text-gray-900"
										)}
									>
										{active && (
											<div className="absolute left-0 top-1/2 -translate-y-1/2 w-1 h-8 bg-blue-500 rounded-r-full"></div>
										)}
										<Icon className={cn(
											"w-5 h-5 transition-colors",
											active ? "text-blue-600" : "text-gray-400 group-hover:text-gray-600"
										)} />
										<div className="flex-1">
											<span className="font-medium">{item.label}</span>
											<p className="text-xs text-gray-500 mt-0.5">{item.description}</p>
										</div>
										<ChevronRight className={cn(
											"w-4 h-4 transition-transform group-hover:translate-x-0.5",
											active ? "text-blue-600" : "text-gray-300 group-hover:text-gray-400"
										)} />
									</Link>
								);
							})}
						</div>
					</div>

					{/* Ações Rápidas */}
					<div className="p-4 border-t border-gray-100">
						<h4 className="text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3 flex items-center gap-2">
							<span className="w-2 h-2 bg-green-500 rounded-full"></span>
							Ações Rápidas
						</h4>
						<div className="space-y-2">
							{quickActions.map((action) => {
								const Icon = action.icon;
								const colorClasses = {
									blue: "hover:bg-blue-50 hover:border-blue-200 text-blue-700 hover:shadow-md",
									orange: "hover:bg-orange-50 hover:border-orange-200 text-orange-700 hover:shadow-md",
									green: "hover:bg-green-50 hover:border-green-200 text-green-700 hover:shadow-md"
								};

								if (action.isExternal) {
									return (
										<PlantaoLink
											key={action.id}
											className={cn(
												"flex items-center space-x-3 px-3 py-3 rounded-lg border border-gray-200 transition-all duration-200 hover:scale-[1.02]",
												colorClasses[action.color as keyof typeof colorClasses]
											)}
										>
											<Icon className="w-5 h-5" />
											<div className="flex-1">
												<span className="font-medium">{action.label}</span>
												<p className="text-xs text-gray-500 mt-0.5">{action.description}</p>
											</div>
											<ChevronRight className="w-4 h-4 transition-transform group-hover:translate-x-0.5" />
										</PlantaoLink>
									);
								}

								return (
									<Link
										key={action.id}
										href={action.href}
										onClick={handleItemClick}
										className={cn(
											"flex items-center space-x-3 px-3 py-3 rounded-lg border border-gray-200 transition-all duration-200 hover:scale-[1.02]",
											colorClasses[action.color as keyof typeof colorClasses]
										)}
									>
										<Icon className="w-5 h-5" />
										<div className="flex-1">
											<span className="font-medium">{action.label}</span>
											<p className="text-xs text-gray-500 mt-0.5">{action.description}</p>
										</div>
										<ChevronRight className="w-4 h-4 transition-transform group-hover:translate-x-0.5" />
									</Link>
								);
							})}
						</div>
					</div>

					{/* Perfil e Configurações */}
					<div className="p-4 border-t border-gray-100">
						<h4 className="text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3 flex items-center gap-2">
							<span className="w-2 h-2 bg-purple-500 rounded-full"></span>
							Conta
						</h4>
						<div className="space-y-1">
							{profileActions.map((action) => {
								const Icon = action.icon;
								const showNotification = action.id === 'notifications' && hasNotifications;

								return (
									<Link
										key={action.id}
										href={action.href}
										onClick={handleItemClick}
										className="flex items-center space-x-3 px-3 py-3 rounded-lg transition-all duration-200 hover:bg-gray-50 hover:shadow-sm hover:scale-[1.02] text-gray-700 hover:text-gray-900 group relative"
									>
										<Icon className="w-5 h-5 text-gray-400 group-hover:text-gray-600" />
										<div className="flex-1">
											<span className="font-medium">{action.label}</span>
											<p className="text-xs text-gray-500 mt-0.5">{action.description}</p>
										</div>
										{showNotification && (
											<div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
										)}
										<ChevronRight className="w-4 h-4 text-gray-300 group-hover:text-gray-400 transition-transform group-hover:translate-x-0.5" />
									</Link>
								);
							})}
						</div>
					</div>

					{/* Sair */}
					<div className="p-4 border-t border-gray-100 pb-10">
						<Link
							href="/auth/logout"
							onClick={handleItemClick}
							className="flex items-center space-x-3 px-3 py-3 rounded-lg transition-all duration-200 hover:bg-red-50 hover:shadow-sm hover:scale-[1.02] text-red-600 hover:text-red-700 group"
						>
							<LogOut className="w-5 h-5" />
							<span className="font-medium">Sair</span>
						</Link>
					</div>
				</div>

				{/* Footer do Sidemenu */}
				<div className="p-4 border-t border-gray-100 bg-gradient-to-r from-gray-50 to-blue-50 flex-shrink-0">
					<div className="text-center">
						<p className="text-xs text-gray-500 mb-2">
							Precisa de ajuda?
						</p>
						<Link
							href="/patient/support"
							onClick={handleItemClick}
							className="inline-flex items-center justify-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-all duration-200 hover:scale-105 shadow-sm"
						>
							<HelpCircle className="w-4 h-4 mr-2" />
							Suporte
						</Link>
					</div>
				</div>
			</div>
		</>
	);
}
