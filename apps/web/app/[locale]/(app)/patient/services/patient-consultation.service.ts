// Remover imports incorretos e usar tipos locais
export interface ActiveConsultation {
  id: string;
  doctor: {
    name: string;
    specialty: string;
    image?: string;
    crm: string;
  };
  status: "active" | "waiting" | "ended";
  isOnline: boolean;
  lastMessage?: {
    content: string;
    createdAt: string;
    isFromDoctor: boolean;
  };
  unreadCount: number;
  scheduledAt: string;
}

export interface ConsultationHistory {
  id: string;
  doctor: {
    name: string;
    specialty: string;
    image?: string | null;
  };
  scheduledAt: string;
  status: string; // Usar string genérico em vez de AppointmentStatus
  type: "REGULAR" | "ON_DUTY";
  amount: number;
  paymentStatus: "PENDING" | "PAID" | "REFUNDED";
}

export interface ConsultationData {
  id: string;
  status: string;
  scheduledAt: string;
  amount: number;
  paymentStatus: string;
  isOnDuty: boolean;
  urgencyLevel?: string;
  consultType?: string;
  appointmentType?: string;
  symptoms?: string;
  duration?: number;
  doctor: {
    id: string;
    name: string;
    specialty: string;
    avatarUrl?: string;
    crm: string;
    onlineStatus: string;
  };
  hospital?: {
    id: string;
    name: string;
    logoUrl?: string;
  };
  lastMessage?: {
    content: string;
    createdAt: string;
    isFromDoctor: boolean;
  };
  unreadCount: number;
  roomId?: string;
  chatEnabled?: boolean;
  recordingEnabled?: boolean;
}

export interface ApiResponse {
  success: boolean;
  data: ConsultationData[];
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export class PatientConsultationService {
  private baseUrl = '/api/patient';

  /**
   * Buscar consultas ativas do paciente
   */
  async getActiveConsultations(userId: string): Promise<ConsultationData[]> {
    try {
      console.log('Buscando consultas ativas para userId:', userId);
      const url = `${this.baseUrl}/consultations?userId=${userId}&status=active`;
      console.log('URL da requisição:', url);

      const response = await fetch(url);

      console.log('Resposta da API:', {
        status: response.status,
        ok: response.ok,
        statusText: response.statusText
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Erro na resposta da API:', errorText);
        throw new Error(`HTTP error! status: ${response.status} - ${errorText}`);
      }

      const result: ApiResponse = await response.json();
      console.log('Resultado da API:', result);

      if (!result.success) {
        console.error('API retornou erro:', result);
        throw new Error('API retornou erro');
      }

      console.log('Consultas ativas encontradas:', result.data.length);
      return result.data;
    } catch (error) {
      console.error('Erro ao buscar consultas ativas:', error);
      throw new Error('Não foi possível carregar as consultas ativas');
    }
  }

  /**
   * Buscar histórico de consultas do paciente
   */
  async getConsultationHistory(userId: string, page: number = 1): Promise<ConsultationData[]> {
    try {
      console.log('Buscando histórico de consultas para userId:', userId, 'página:', page);
      const url = `${this.baseUrl}/consultations?userId=${userId}&status=completed&page=${page}`;
      console.log('URL da requisição:', url);

      const response = await fetch(url);

      console.log('Resposta da API:', {
        status: response.status,
        ok: response.ok,
        statusText: response.statusText
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Erro na resposta da API:', errorText);
        throw new Error(`HTTP error! status: ${response.status} - ${errorText}`);
      }

      const result: ApiResponse = await response.json();
      console.log('Resultado da API:', result);

      if (!result.success) {
        console.error('API retornou erro:', result);
        throw new Error('API retornou erro');
      }

      console.log('Histórico de consultas encontrado:', result.data.length);
      return result.data;
    } catch (error) {
      console.error('Erro ao buscar histórico de consultas:', error);
      throw new Error('Não foi possível carregar o histórico de consultas');
    }
  }

  /**
   * Buscar próximas consultas agendadas
   */
  async getUpcomingConsultations(userId: string): Promise<ConsultationData[]> {
    try {
      console.log('Buscando próximas consultas para userId:', userId);
      const url = `${this.baseUrl}/consultations?userId=${userId}&status=scheduled`;
      console.log('URL da requisição:', url);

      const response = await fetch(url);

      console.log('Resposta da API:', {
        status: response.status,
        ok: response.ok,
        statusText: response.statusText
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Erro na resposta da API:', errorText);
        throw new Error(`HTTP error! status: ${response.status} - ${errorText}`);
      }

      const result: ApiResponse = await response.json();
      console.log('Resultado da API:', result);

      if (!result.success) {
        console.error('API retornou erro:', result);
        throw new Error('API retornou erro');
      }

      console.log('Próximas consultas encontradas:', result.data.length);
      return result.data;
    } catch (error) {
      console.error('Erro ao buscar próximas consultas:', error);
      throw new Error('Não foi possível carregar as próximas consultas');
    }
  }

  /**
   * Buscar consulta específica por ID
   */
  async getConsultationById(consultationId: string): Promise<ConsultationData | null> {
    try {
      const response = await fetch(`${this.baseUrl}/consultations/${consultationId}`);

      if (!response.ok) {
        if (response.status === 404) {
          return null;
        }
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: ApiResponse = await response.json();

      if (!result.success || !result.data.length) {
        return null;
      }

      return result.data[0];
    } catch (error) {
      console.error('Erro ao buscar consulta:', error);
      throw new Error('Não foi possível carregar a consulta');
    }
  }

  /**
   * Transformar dados da API para formato do componente
   */
  private transformToActiveConsultations(data: ConsultationData[]): ActiveConsultation[] {
    return data.map(item => ({
      id: item.id,
      doctor: {
        name: item.doctor.name,
        specialty: item.doctor.specialty,
        image: item.doctor.avatarUrl,
        crm: item.doctor.crm
      },
      status: this.mapAppointmentStatus(item.status),
      isOnline: item.doctor.onlineStatus === 'ONLINE',
      lastMessage: item.lastMessage,
      unreadCount: item.unreadCount,
      scheduledAt: item.scheduledAt
    }));
  }

  /**
   * Transformar dados para histórico de consultas
   */
  private transformToConsultationHistory(data: ConsultationData[]): ConsultationHistory[] {
    return data.map(item => ({
      id: item.id,
      doctor: {
        name: item.doctor.name,
        specialty: item.doctor.specialty,
        image: item.doctor.avatarUrl || null
      },
      scheduledAt: item.scheduledAt,
      status: item.status,
      type: item.isOnDuty ? 'ON_DUTY' : 'REGULAR',
      amount: item.amount,
      paymentStatus: item.paymentStatus as "PENDING" | "PAID" | "REFUNDED"
    }));
  }

  /**
   * Mapear status do banco para status do componente
   */
  private mapAppointmentStatus(status: string): "active" | "waiting" | "ended" {
    switch (status) {
      case 'IN_PROGRESS':
        return 'active';
      case 'SCHEDULED':
      case 'WAITING_ON_DUTY':
      case 'ACCEPTED_BY_DOCTOR':
        return 'waiting';
      case 'COMPLETED':
      case 'CANCELED':
      case 'NO_SHOW':
        return 'ended';
      default:
        return 'waiting';
    }
  }

  /**
   * Verificar se o paciente tem consultas ativas
   */
  async hasActiveConsultations(userId: string): Promise<boolean> {
    try {
      const consultations = await this.getActiveConsultations(userId);
      return consultations.length > 0;
    } catch (error) {
      console.error('Erro ao verificar consultas ativas:', error);
      return false;
    }
  }

  /**
   * Buscar estatísticas das consultas do paciente
   */
  async getConsultationStats(userId: string): Promise<{
    total: number;
    completed: number;
    upcoming: number;
    active: number;
  }> {
    try {
      const [active, upcoming, completed] = await Promise.all([
        this.getActiveConsultations(userId),
        this.getUpcomingConsultations(userId),
        this.getConsultationHistory(userId)
      ]);

      return {
        total: active.length + upcoming.length + completed.length,
        completed: completed.length,
        upcoming: upcoming.length,
        active: active.length
      };
    } catch (error) {
      console.error('Erro ao buscar estatísticas:', error);
      return {
        total: 0,
        completed: 0,
        upcoming: 0,
        active: 0
      };
    }
  }
}
