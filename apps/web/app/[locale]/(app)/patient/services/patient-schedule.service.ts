import { PatientConsultationService } from './patient-consultation.service';

export interface DoctorAvailable {
  id: string;
  name: string;
  specialty: string;
  image?: string;
  crm: string;
  rating: number;
  reviewCount: number;
  price: number;
  location: string;
  availableSlots: string[];
  isOnline: boolean;
}

export interface Specialty {
  id: string;
  name: string;
  icon: string;
  description: string;
}

export interface TimeSlot {
  time: string;
  available: boolean;
  price?: number;
}

export interface BookingRequest {
  doctorId: string;
  patientId: string;
  scheduledAt: string;
  specialty?: string;
  isOnDuty?: boolean;
  notes?: string;
}

export class PatientScheduleService {
  private consultationService = new PatientConsultationService();
  private baseUrl = '/api/patient';

  /**
   * Buscar especialidades disponíveis
   */
  async getSpecialties(): Promise<Specialty[]> {
    try {
      // Usar tRPC via endpoint interno para garantir as mesmas especialidades do público
      const response = await fetch('/api/doctors/specialties');
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      return this.transformSpecialties(data);
    } catch (error) {
      console.error('Erro ao buscar especialidades:', error);
      return this.getFallbackSpecialties();
    }
  }

  /**
   * Buscar médicos disponíveis por especialidade
   */
  async getAvailableDoctors(specialty?: string, searchTerm?: string, page: number = 1): Promise<{ doctors: DoctorAvailable[]; pagination?: any; }> {
    try {
      // Reusar o mesmo fluxo de listagem pública (tRPC publicList) exposto via /api/doctors
      let url = `/api/doctors?available=true&page=${page}&perPage=6`;

      if (specialty) {
        url += `&specialty=${encodeURIComponent(specialty)}`;
      }

      if (searchTerm) {
        url += `&search=${encodeURIComponent(searchTerm)}`;
      }

      const response = await fetch(url);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      const doctors = this.transformDoctors(data.doctors || data);
      return { doctors, pagination: data.pagination };
    } catch (error) {
      console.error('Erro ao buscar médicos:', error);
      return { doctors: this.getFallbackDoctors(specialty) };
    }
  }

  /**
   * Buscar horários disponíveis para um médico em uma data
   */
  async getAvailableSlots(doctorId: string, date: string): Promise<TimeSlot[]> {
    try {
      const response = await fetch(`/api/doctors/${doctorId}/availability?date=${date}`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return this.transformTimeSlots(data);
    } catch (error) {
      console.error('Erro ao buscar horários:', error);
      // Fallback para horários mock
      return this.getFallbackTimeSlots();
    }
  }

  /**
   * Agendar consulta
   */
  async bookAppointment(bookingData: BookingRequest): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}/consultations`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(bookingData),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const appointment = await response.json();
      return appointment;
    } catch (error) {
      console.error('Erro ao agendar consulta:', error);
      throw new Error('Não foi possível agendar a consulta');
    }
  }

  /**
   * Buscar detalhes de um médico
   */
  async getDoctorDetails(doctorId: string): Promise<DoctorAvailable | null> {
    try {
      const response = await fetch(`/api/doctors/${doctorId}`);

      if (!response.ok) {
        if (response.status === 404) {
          return null;
        }
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return this.transformDoctor(data);
    } catch (error) {
      console.error('Erro ao buscar detalhes do médico:', error);
      return null;
    }
  }

  /**
   * Verificar disponibilidade em tempo real
   */
  async checkRealTimeAvailability(doctorId: string, scheduledAt: string): Promise<boolean> {
    try {
      const response = await fetch(`/api/doctors/${doctorId}/check-availability`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ scheduledAt }),
      });

      if (!response.ok) {
        return false;
      }

      const { available } = await response.json();
      return available;
    } catch (error) {
      console.error('Erro ao verificar disponibilidade:', error);
      return false;
    }
  }

  // Métodos privados para transformação de dados

  private transformSpecialties(data: any[]): Specialty[] {
    return data.map(item => ({
      id: item.id,
      name: item.name,
      icon: this.getSpecialtyIcon(item.name),
      description: item.description || `Cuidados em ${item.name.toLowerCase()}`
    }));
  }

  private transformDoctors(data: any[]): DoctorAvailable[] {
    return data.map(item => this.transformDoctor(item));
  }

  private transformDoctor(item: any): DoctorAvailable {
    const name = item.name || item.user?.name || 'Médico';
    const avatarUrl = item.user?.avatarUrl;

    // Usar o padrão correto do Supabase para URLs de avatar
    const imageUrl = avatarUrl
      ? `https://moupvfqlulvqbzwajkif.supabase.co/storage/v1/object/public/avatars/${avatarUrl}`
      : this.generateAvatarUrl(name);


    return {
      id: item.id,
      name: name,
      specialty: item.specialties?.[0]?.name || item.specialty || 'Especialidade não informada',
      image: imageUrl,
      crm: `${item.crm}/${item.crmState}` || 'CRM não informado',
      rating: item.rating || 4.5,
      reviewCount: item.totalRatings || 0,
      price: item.consultationPrice || item.price || 150,
      location: 'Online',
      availableSlots: this.getFallbackTimeSlots().map(slot => slot.time),
      isOnline: item.onlineStatus === 'ONLINE'
    };
  }

  private generateAvatarUrl(name: string): string {
    const initials = name.split(' ').map(n => n[0]).join('').toUpperCase();
    return `https://api.dicebear.com/7.x/initials/svg?seed=${encodeURIComponent(initials)}&backgroundColor=4f46e5&textColor=ffffff`;
  }

  private transformTimeSlots(data: any[]): TimeSlot[] {
    return data.map(slot => ({
      time: slot.time,
      available: slot.available,
      price: slot.price
    }));
  }

  private getSpecialtyIcon(name: string): string {
    const icons: Record<string, string> = {
      'Cardiologia': '❤️',
      'Dermatologia': '🧴',
      'Ginecologia': '🌸',
      'Pediatria': '👶',
      'Clínica Geral': '🩺',
      'Psiquiatria': '🧠',
      'Neurologia': '🧠',
      'Ortopedia': '🦴',
      'Oftalmologia': '👁️',
      'Otorrinolaringologia': '👂',
    };
    return icons[name] || '🩺';
  }

  // Fallback data para desenvolvimento

  private getFallbackSpecialties(): Specialty[] {
    return [
      {
        id: "cardiologia",
        name: "Cardiologia",
        icon: "❤️",
        description: "Cuidados com o coração"
      },
      {
        id: "dermatologia",
        name: "Dermatologia",
        icon: "🧴",
        description: "Cuidados com a pele"
      },
      {
        id: "ginecologia",
        name: "Ginecologia",
        icon: "🌸",
        description: "Saúde da mulher"
      },
      {
        id: "pediatria",
        name: "Pediatria",
        icon: "👶",
        description: "Cuidados infantis"
      },
      {
        id: "clinica-geral",
        name: "Clínica Geral",
        icon: "🩺",
        description: "Cuidados gerais"
      },
      {
        id: "psiquiatria",
        name: "Psiquiatria",
        icon: "🧠",
        description: "Saúde mental"
      }
    ];
  }

  private getFallbackDoctors(specialty?: string): DoctorAvailable[] {
    const doctors = [
      {
        id: "1",
        name: "Dr. João Silva",
        specialty: "Cardiologia",
        image: "/api/placeholder/100/100",
        crm: "CRM/SP 123456",
        rating: 4.8,
        reviewCount: 127,
        price: 180,
        location: "Online",
        availableSlots: ["09:00", "10:00", "14:00", "15:00"],
        isOnline: true
      },
      {
        id: "2",
        name: "Dra. Maria Santos",
        specialty: "Dermatologia",
        image: "/api/placeholder/100/100",
        crm: "CRM/SP 789012",
        rating: 4.9,
        reviewCount: 89,
        price: 160,
        location: "Online",
        availableSlots: ["08:00", "11:00", "16:00"],
        isOnline: true
      },
      {
        id: "3",
        name: "Dr. Pedro Costa",
        specialty: "Clínica Geral",
        image: "/api/placeholder/100/100",
        crm: "CRM/SP 345678",
        rating: 4.7,
        reviewCount: 203,
        price: 120,
        location: "Online",
        availableSlots: ["09:30", "13:00", "17:00"],
        isOnline: true
      }
    ];

    if (specialty) {
      return doctors.filter(doc =>
        doc.specialty.toLowerCase().includes(specialty.toLowerCase())
      );
    }

    return doctors;
  }

  private getFallbackTimeSlots(): TimeSlot[] {
    return [
      { time: "08:00", available: true, price: 150 },
      { time: "09:00", available: true, price: 150 },
      { time: "10:00", available: false },
      { time: "11:00", available: true, price: 150 },
      { time: "14:00", available: true, price: 150 },
      { time: "15:00", available: true, price: 150 },
      { time: "16:00", available: false },
      { time: "17:00", available: true, price: 150 },
    ];
  }
}
