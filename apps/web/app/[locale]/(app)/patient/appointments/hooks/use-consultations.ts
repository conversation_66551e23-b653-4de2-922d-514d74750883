import { useState, useEffect, useCallback, useMemo } from 'react';
import { PatientConsultationService, ConsultationData } from '../../services/patient-consultation.service';

interface UseConsultationsOptions {
  userId: string;
  autoRefresh?: boolean;
  refreshInterval?: number;
}

interface UseConsultationsReturn {
  consultations: ConsultationData[];
  isLoading: boolean;
  isRefreshing: boolean;
  error: string | null;
  stats: {
    total: number;
    upcoming: number;
    completed: number;
    cancelled: number;
    pending: number;
  };
  loadConsultations: (showLoading?: boolean) => Promise<void>;
  refreshConsultations: () => Promise<void>;
  clearError: () => void;
}

export function useConsultations({
  userId,
  autoRefresh = true,
  refreshInterval = 30000
}: UseConsultationsOptions): UseConsultationsReturn {
  const [consultations, setConsultations] = useState<ConsultationData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const service = useMemo(() => new PatientConsultationService(), []);

  const loadConsultations = useCallback(async (showLoading = true) => {
    try {
      if (showLoading) setIsLoading(true);
      setError(null);

      console.log('Carregando consultas para userId:', userId);

      const [upcoming, completed, active] = await Promise.all([
        service.getUpcomingConsultations(userId),
        service.getConsultationHistory(userId),
        service.getActiveConsultations(userId)
      ]);

      console.log('Consultas carregadas:', {
        upcoming: upcoming.length,
        completed: completed.length,
        active: active.length
      });

      const allConsultations = [...upcoming, ...completed, ...active];
      setConsultations(allConsultations);
    } catch (error) {
      console.error("Erro ao carregar consultas:", error);
      console.error("Detalhes do erro:", error instanceof Error ? error.message : 'Erro desconhecido');
      setError("Erro ao carregar consultas. Tente novamente.");
    } finally {
      setIsLoading(false);
    }
  }, [userId, service]);

  const refreshConsultations = useCallback(async () => {
    setIsRefreshing(true);
    await loadConsultations(false);
    setIsRefreshing(false);
  }, [loadConsultations]);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // Auto-refresh
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(() => {
      refreshConsultations();
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval, refreshConsultations]);

  // Carregamento inicial
  useEffect(() => {
    loadConsultations();
  }, [loadConsultations]);

  // Estatísticas calculadas
  const stats = useMemo(() => {
    const upcoming = consultations.filter(c => c.status === "SCHEDULED").length;
    const completed = consultations.filter(c => c.status === "COMPLETED").length;
    const cancelled = consultations.filter(c => c.status === "CANCELED").length;
    const pending = consultations.filter(c =>
      c.status === "WAITING_ON_DUTY" || c.status === "ACCEPTED_BY_DOCTOR"
    ).length;

    return {
      total: consultations.length,
      upcoming,
      completed,
      cancelled,
      pending
    };
  }, [consultations]);

  return {
    consultations,
    isLoading,
    isRefreshing,
    error,
    stats,
    loadConsultations,
    refreshConsultations,
    clearError
  };
}
