"use client";

import type { User } from 'database';
import { Card, CardContent } from "../../components/ui/card";
import { Button } from "../../components/ui/button";
import {
	Calendar,
	Clock,
	Video,
	Plus,
	Stethoscope,
	AlertCircle,
	ChevronRight,
	Search,
	Filter,
	RefreshCw
} from "lucide-react";
import { useState, useMemo } from "react";
import Link from "next/link";
import { ConsultationData } from "../../services/patient-consultation.service";
import { PageHeader } from "../../components/ui/page-header";
import { PlantaoLink } from "../../components/plantao-link";
import { AppointmentDetailsSheet } from "./appointment-details-sheet";
import { useConsultations } from "../hooks/use-consultations";
import {
	ConsultationsListSkeleton,
	SummaryCardsSkeleton,
	ActionCardsSkeleton
} from "./consultation-skeleton";
import { toast } from "sonner";

export default function PatientAppointments({ user }: { user: User }) {
	const [statusFilter, setStatusFilter] = useState<string>("all");
	const [searchQuery, setSearchQuery] = useState("");

	const {
		consultations,
		isLoading,
		isRefreshing,
		error,
		stats,
		loadConsultations,
		refreshConsultations,
		clearError
	} = useConsultations({
		userId: user.id,
		autoRefresh: true,
		refreshInterval: 30000
	});

	// Filtrar consultas baseado no status e busca
	const filteredConsultations = useMemo(() => {
		return consultations.filter(consultation => {
			const matchesStatus = statusFilter === "all" || consultation.status === statusFilter;
			const matchesSearch = searchQuery === "" ||
				consultation.doctor.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
				consultation.doctor.specialty.toLowerCase().includes(searchQuery.toLowerCase());

			return matchesStatus && matchesSearch;
		});
	}, [consultations, statusFilter, searchQuery]);

	const upcomingConsultations = useMemo(() =>
		filteredConsultations.filter(c => c.status === "SCHEDULED"),
		[filteredConsultations]
	);

	const activeConsultations = useMemo(() =>
		filteredConsultations.filter(c =>
			c.status === "IN_PROGRESS" ||
			c.status === "WAITING_ON_DUTY" ||
			c.status === "ACCEPTED_BY_DOCTOR"
		),
		[filteredConsultations]
	);

	const getStatusColor = (status: string) => {
		switch (status) {
			case "SCHEDULED":
				return "bg-blue-100 text-blue-800 border-blue-200";
			case "COMPLETED":
				return "bg-green-100 text-green-800 border-green-200";
			case "CANCELED":
				return "bg-red-100 text-red-800 border-red-200";
			case "WAITING_ON_DUTY":
			case "ACCEPTED_BY_DOCTOR":
				return "bg-orange-100 text-orange-800 border-orange-200";
			case "IN_PROGRESS":
				return "bg-green-100 text-green-800 border-green-200";
			default:
				return "bg-gray-100 text-gray-800 border-gray-200";
		}
	};

	const getStatusText = (status: string) => {
		switch (status) {
			case "SCHEDULED":
				return "Agendada";
			case "COMPLETED":
				return "Concluída";
			case "CANCELED":
				return "Cancelada";
			case "WAITING_ON_DUTY":
				return "Aguardando Plantão";
			case "ACCEPTED_BY_DOCTOR":
				return "Aceita pelo Médico";
			case "IN_PROGRESS":
				return "Em Andamento";
			default:
				return "Desconhecido";
		}
	};

	const getConsultationTypeIcon = (type: string) => {
		switch (type) {
			case "TELEMEDICINE":
				return <Video className="h-4 w-4" />;
			case "AMBULATORY":
				return <Stethoscope className="h-4 w-4" />;
			case "PRE_ANESTHETIC":
				return <AlertCircle className="h-4 w-4" />;
			default:
				return <Calendar className="h-4 w-4" />;
		}
	};

	const getConsultationTypeText = (type: string) => {
		switch (type) {
			case "TELEMEDICINE":
				return "Teleconsulta";
			case "AMBULATORY":
				return "Presencial";
			case "PRE_ANESTHETIC":
				return "Pré-Anestésica";
			default:
				return "Consulta";
		}
	};

	const handleJoinConsultation = (appointmentId: string) => {
		// TODO: Implementar navegação para sala de consulta
		toast.success("Entrando na consulta...");
		console.log("Entrando na consulta:", appointmentId);
	};

	const handleCancelAppointment = (appointmentId: string) => {
		// TODO: Implementar cancelamento de consulta
		toast.success("Solicitação de cancelamento enviada");
		console.log("Cancelando consulta:", appointmentId);
	};

	const handleRescheduleAppointment = (appointmentId: string) => {
		// TODO: Implementar reagendamento
		toast.success("Redirecionando para reagendamento...");
		console.log("Reagendando consulta:", appointmentId);
	};

	if (error) {
		return (
			<div className="container mx-auto max-w-6xl px-4 py-4 sm:py-6">
				<div className="text-center py-8 sm:py-12">
					<AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
					<h3 className="text-lg font-medium text-gray-900 mb-2">Erro ao carregar consultas</h3>
					<p className="text-gray-600 mb-6">{error}</p>
					<div className="flex flex-col sm:flex-row gap-3 justify-center">
						<Button onClick={() => loadConsultations()} className="bg-blue-600 hover:bg-blue-700">
							<RefreshCw className="h-4 w-4 mr-2" />
							Tentar Novamente
						</Button>
						<Button onClick={clearError} variant="outline">
							Limpar Erro
						</Button>
					</div>
				</div>
			</div>
		);
	}

	return (
		<div className="container mx-auto max-w-6xl px-3 sm:px-4 py-4 sm:py-6">
			<div className="space-y-4 sm:space-y-6">
				{/* Header da Página */}
				<PageHeader
					title="Minhas Consultas"
					subtitle="Acompanhe seus agendamentos e histórico médico"
					showFilter={true}
					showNewButton={true}
					newButtonHref="/patient/schedule"
					filterContent={
						<div className="space-y-4">
							{/* Barra de Busca */}
							<div className="relative">
								<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
								<input
									type="text"
									placeholder="Buscar por médico ou especialidade..."
									value={searchQuery}
									onChange={(e) => setSearchQuery(e.target.value)}
									className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-base"
								/>
							</div>

							{/* Filtro de Status */}
							<div>
								<label className="text-sm font-medium mb-2 block flex items-center gap-2">
									<Filter className="h-4 w-4" />
									Status
								</label>
								<select
									value={statusFilter}
									onChange={(e) => setStatusFilter(e.target.value)}
									className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-base"
								>
									<option value="all">Todos os status</option>
									<option value="SCHEDULED">Agendadas</option>
									<option value="WAITING_ON_DUTY">Aguardando Plantão</option>
									<option value="ACCEPTED_BY_DOCTOR">Aceitas pelo Médico</option>
									<option value="IN_PROGRESS">Em Andamento</option>
									<option value="COMPLETED">Concluídas</option>
									<option value="CANCELED">Canceladas</option>
								</select>
							</div>

							{/* Botão de Atualizar */}
							<Button
								onClick={refreshConsultations}
								disabled={isRefreshing}
								variant="outline"
								className="w-full py-3"
							>
								<RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
								{isRefreshing ? 'Atualizando...' : 'Atualizar'}
							</Button>
						</div>
					}
				/>

				{/* Action Cards - Mobile Optimized */}
				{isLoading ? (
					<ActionCardsSkeleton />
				) : (
					<div className="space-y-3 sm:space-y-4">
						{/* Card Agendar Nova Consulta */}
						<Card className="overflow-hidden hover:shadow-lg transition-all duration-200 cursor-pointer group border-0 shadow-sm hover:shadow-md">
							<CardContent className="p-4 sm:p-6">
								<Link href="/patient/schedule" className="block">
									<div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
										<div className="flex items-start sm:items-center gap-3 sm:gap-4">
											<div className="w-14 h-14 sm:w-16 sm:h-16 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-200 flex-shrink-0">
												<Plus className="h-7 w-7 sm:h-8 sm:w-8 text-white" />
											</div>
											<div className="flex-1 min-w-0">
												<h3 className="text-lg sm:text-xl font-bold text-gray-900 group-hover:text-emerald-700 transition-colors leading-tight">
													Agendar Nova Consulta
												</h3>
												<p className="text-gray-600 mt-1 text-sm sm:text-base leading-relaxed">
													Escolha um médico e horário disponível
												</p>
												<div className="flex flex-wrap items-center gap-2 mt-3">
													<span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-emerald-100 text-emerald-800">
														Disponível 24h
													</span>
													<span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
														Agendamento Online
													</span>
												</div>
											</div>
										</div>
										<div className="flex items-center justify-between sm:justify-end gap-3 sm:gap-2">
											<div className="text-right">
												<p className="text-xs sm:text-sm text-gray-500">Tempo médio</p>
												<p className="text-base sm:text-lg font-semibold text-emerald-600">2 min</p>
											</div>
											<ChevronRight className="h-5 w-5 sm:h-6 sm:w-6 text-gray-400 group-hover:text-emerald-600 transition-colors group-hover:translate-x-1 flex-shrink-0" />
										</div>
									</div>
								</Link>
							</CardContent>
						</Card>

						{/* Card Plantão Médico */}
						<Card className="overflow-hidden hover:shadow-lg transition-all duration-200 cursor-pointer group border-0 shadow-sm hover:shadow-md border-l-4 border-l-orange-500">
							<CardContent className="p-4 sm:p-6">
								<PlantaoLink urgencyLevel="high" className="block">
									<div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
										<div className="flex items-start sm:items-center gap-3 sm:gap-4">
											<div className="w-14 h-14 sm:w-16 sm:h-16 bg-gradient-to-br from-orange-500 to-red-500 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-200 flex-shrink-0">
												<div className="relative">
													<Stethoscope className="h-7 w-7 sm:h-8 sm:w-8 text-white" />
													<div className="absolute -top-1 -right-1 w-3 h-3 sm:w-4 sm:h-4 bg-red-500 rounded-full animate-pulse"></div>
												</div>
											</div>
											<div className="flex-1 min-w-0">
												<h3 className="text-lg sm:text-xl font-bold text-gray-900 group-hover:text-orange-700 transition-colors leading-tight">
													Plantão Médico 24h
												</h3>
												<p className="text-gray-600 mt-1 text-sm sm:text-base leading-relaxed">
													Atendimento imediato para emergências
												</p>
												<div className="flex flex-wrap items-center gap-2 mt-3">
													<span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 animate-pulse">
														URGENTE
													</span>
													<span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
														Resposta em 5 min
													</span>
												</div>
											</div>
										</div>
										<div className="flex items-center justify-between sm:justify-end gap-3 sm:gap-2">
											<div className="text-right">
												<p className="text-xs sm:text-sm text-gray-500">Status</p>
												<p className="text-base sm:text-lg font-semibold text-green-600">Online</p>
											</div>
											<ChevronRight className="h-5 w-5 sm:h-6 sm:w-6 text-gray-400 group-hover:text-orange-600 transition-colors group-hover:translate-x-1 flex-shrink-0" />
										</div>
									</div>
								</PlantaoLink>
							</CardContent>
						</Card>
					</div>
				)}

				{/* Summary Cards - Mobile Optimized */}
				{isLoading ? (
					<SummaryCardsSkeleton />
				) : (
					<div className="grid grid-cols-2 gap-3 sm:gap-4">
						<Card className="text-center p-3 sm:p-4">
							<div className="flex items-center justify-between">
								<div>
									<p className="text-xs sm:text-sm text-gray-600 mb-1">Próximas</p>
									<p className="text-xl sm:text-2xl font-bold text-blue-600">{stats.upcoming}</p>
								</div>
								<div className="w-10 h-10 sm:w-12 sm:h-12 bg-blue-100 rounded-full flex items-center justify-center">
									<Calendar className="h-5 w-5 sm:h-6 sm:w-6 text-blue-600" />
								</div>
							</div>
						</Card>

						<Card className="text-center p-3 sm:p-4">
							<div className="flex items-center justify-between">
								<div>
									<p className="text-xs sm:text-sm text-gray-600 mb-1">Concluídas</p>
									<p className="text-xl sm:text-2xl font-bold text-green-600">{stats.completed}</p>
								</div>
								<div className="w-10 h-10 sm:w-12 sm:h-12 bg-green-100 rounded-full flex items-center justify-center">
									<Clock className="h-5 w-5 sm:h-6 sm:w-6 text-green-600" />
								</div>
							</div>
						</Card>

						<Card className="text-center p-3 sm:p-4">
							<div className="flex items-center justify-between">
								<div>
									<p className="text-xs sm:text-sm text-gray-600 mb-1">Canceladas</p>
									<p className="text-xl sm:text-2xl font-bold text-red-600">{stats.cancelled}</p>
								</div>
								<div className="w-10 h-10 sm:w-12 sm:h-12 bg-red-100 rounded-full flex items-center justify-center">
									<AlertCircle className="h-5 w-5 sm:h-6 sm:w-6 text-red-600" />
								</div>
							</div>
						</Card>

						<Card className="text-center p-3 sm:p-4">
							<div className="flex items-center justify-between">
								<div>
									<p className="text-xs sm:text-sm text-gray-600 mb-1">Pendentes</p>
									<p className="text-xl sm:text-2xl font-bold text-yellow-600">{stats.pending}</p>
								</div>
								<div className="w-10 h-10 sm:w-12 sm:h-12 bg-yellow-100 rounded-full flex items-center justify-center">
									<Stethoscope className="h-5 w-5 sm:h-6 sm:w-6 text-yellow-600" />
								</div>
							</div>
						</Card>
					</div>
				)}

				{/* Upcoming Consultations - Mobile Optimized */}
				{!isLoading && upcomingConsultations.length > 0 && (
					<Card>
						<CardContent className="p-4 sm:p-6">
							<div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 mb-4 sm:mb-6">
								<h2 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
									<Calendar className="h-5 w-5 text-blue-600" />
									Próximas Consultas
								</h2>
								<span className="text-sm text-gray-500 bg-blue-50 px-3 py-1 rounded-full self-start sm:self-auto">
									{upcomingConsultations.length} agendada{upcomingConsultations.length !== 1 ? 's' : ''}
								</span>
							</div>
							<div className="space-y-3 sm:space-y-4">
								{upcomingConsultations.map((consultation) => (
									<Card key={consultation.id} className="overflow-hidden border-l-4 border-l-blue-500 hover:shadow-md transition-shadow">
										<CardContent className="p-3 sm:p-4">
											<div className="space-y-3">
												<div className="flex items-start justify-between">
													<div className="flex items-start gap-3 flex-1 min-w-0">
														<div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
															<Stethoscope className="h-5 w-5 text-blue-600" />
														</div>
														<div className="flex-1 min-w-0">
															<div className="flex flex-col sm:flex-row sm:items-center gap-2 mb-1">
																<h3 className="font-semibold text-gray-900 text-sm sm:text-base truncate">Dr. {consultation.doctor.name}</h3>
																<span className={`px-2 py-1 text-xs rounded-full border ${getStatusColor(consultation.status)} self-start sm:self-auto`}>
																	{getStatusText(consultation.status)}
																</span>
															</div>
															<p className="text-sm text-gray-600 mb-2">{consultation.doctor.specialty}</p>
															<div className="flex flex-col sm:flex-row gap-2 sm:gap-4 text-xs sm:text-sm text-gray-500">
																<span className="flex items-center gap-1">
																	<Calendar className="h-4 w-4" />
																	{new Date(consultation.scheduledAt).toLocaleDateString('pt-BR')}
																</span>
																<span className="flex items-center gap-1">
																	<Clock className="h-4 w-4" />
																	{new Date(consultation.scheduledAt).toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' })}
																</span>
																<span className="flex items-center gap-1">
																	{getConsultationTypeIcon(consultation.appointmentType || "TELEMEDICINE")}
																	{getConsultationTypeText(consultation.appointmentType || "TELEMEDICINE")}
																</span>
															</div>
														</div>
													</div>
												</div>
												<div className="flex flex-col gap-3 pt-3 border-t">
													{consultation.status === "IN_PROGRESS" || consultation.status === "ACCEPTED_BY_DOCTOR" ? (
														<Button
															onClick={() => handleJoinConsultation(consultation.id)}
															className="w-full sm:w-auto bg-blue-600 hover:bg-blue-700 py-2.5"
														>
															<Video className="h-4 w-4 mr-2" />
															Entrar na Consulta
														</Button>
													) : (
														<Button
															onClick={() => handleJoinConsultation(consultation.id)}
															disabled
															className="w-full sm:w-auto bg-gray-400 cursor-not-allowed py-2.5"
														>
															<Clock className="h-4 w-4 mr-2" />
															Aguardando Horário
														</Button>
													)}

													<AppointmentDetailsSheet
														appointment={consultation}
														onJoinConsultation={handleJoinConsultation}
														onCancelAppointment={handleCancelAppointment}
														onRescheduleAppointment={handleRescheduleAppointment}
													>
														<Button variant="outline" className="w-full sm:w-auto py-2.5">
															Ver Detalhes
														</Button>
													</AppointmentDetailsSheet>
												</div>
											</div>
										</CardContent>
									</Card>
								))}
							</div>
						</CardContent>
					</Card>
				)}

				{/* Active Consultations - Mobile Optimized */}
				{!isLoading && activeConsultations.length > 0 && (
					<Card>
						<CardContent className="p-4 sm:p-6">
							<div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 mb-4 sm:mb-6">
								<h2 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
									<Video className="h-5 w-5 text-green-600" />
									Consultas Ativas
								</h2>
								<span className="text-sm text-gray-500 bg-green-50 px-3 py-1 rounded-full self-start sm:self-auto">
									{activeConsultations.length} ativa{activeConsultations.length !== 1 ? 's' : ''}
								</span>
							</div>
							<div className="space-y-3 sm:space-y-4">
								{activeConsultations.map((consultation) => (
									<Card key={consultation.id} className="overflow-hidden border-l-4 border-l-green-500 hover:shadow-md transition-shadow">
										<CardContent className="p-3 sm:p-4">
											<div className="space-y-3">
												<div className="flex items-start justify-between">
													<div className="flex items-start gap-3 flex-1 min-w-0">
														<div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0">
															<Video className="h-5 w-5 text-green-600" />
														</div>
														<div className="flex-1 min-w-0">
															<div className="flex flex-col sm:flex-row sm:items-center gap-2 mb-1">
																<h3 className="font-semibold text-gray-900 text-sm sm:text-base truncate">Dr. {consultation.doctor.name}</h3>
																<span className={`px-2 py-1 text-xs rounded-full border ${getStatusColor(consultation.status)} self-start sm:self-auto`}>
																	{getStatusText(consultation.status)}
																</span>
															</div>
															<p className="text-sm text-gray-600 mb-2">{consultation.doctor.specialty}</p>
															<div className="flex flex-col sm:flex-row gap-2 sm:gap-4 text-xs sm:text-sm text-gray-500">
																<span className="flex items-center gap-1">
																	<Calendar className="h-4 w-4" />
																	{new Date(consultation.scheduledAt).toLocaleDateString('pt-BR')}
																</span>
																<span className="flex items-center gap-1">
																	<Clock className="h-4 w-4" />
																	{new Date(consultation.scheduledAt).toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' })}
																</span>
																<span className="flex items-center gap-1">
																	{getConsultationTypeIcon(consultation.appointmentType || "TELEMEDICINE")}
																	{getConsultationTypeText(consultation.appointmentType || "TELEMEDICINE")}
																</span>
															</div>
														</div>
													</div>
												</div>
												<div className="flex flex-col gap-3 pt-3 border-t">
													<Button
														onClick={() => handleJoinConsultation(consultation.id)}
														className="w-full sm:w-auto bg-green-600 hover:bg-green-700 py-2.5"
													>
														<Video className="h-4 w-4 mr-2" />
														Entrar na Consulta
													</Button>

													<AppointmentDetailsSheet
														appointment={consultation}
														onJoinConsultation={handleJoinConsultation}
														onCancelAppointment={handleCancelAppointment}
														onRescheduleAppointment={handleRescheduleAppointment}
													>
														<Button variant="outline" className="w-full sm:w-auto py-2.5">
															Ver Detalhes
														</Button>
													</AppointmentDetailsSheet>
												</div>
											</div>
										</CardContent>
									</Card>
								))}
							</div>
						</CardContent>
					</Card>
				)}

				{/* History - Mobile Optimized */}
				{!isLoading && filteredConsultations.filter(c => c.status !== "SCHEDULED").length > 0 && (
					<Card>
						<CardContent className="p-4 sm:p-6">
							<div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 mb-4 sm:mb-6">
								<h2 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
									<Clock className="h-5 w-5 text-gray-600" />
									Histórico
								</h2>
								<span className="text-sm text-gray-500 bg-gray-50 px-3 py-1 rounded-full self-start sm:self-auto">
									{filteredConsultations.filter(c => c.status !== "SCHEDULED").length} consulta{filteredConsultations.filter(c => c.status !== "SCHEDULED").length !== 1 ? 's' : ''}
								</span>
							</div>
							<div className="space-y-3 sm:space-y-4">
								{filteredConsultations
									.filter(c => c.status !== "SCHEDULED")
									.map((consultation) => (
										<Card key={consultation.id} className="overflow-hidden hover:shadow-md transition-shadow">
											<CardContent className="p-3 sm:p-4">
												<div className="space-y-3">
													<div className="flex items-start justify-between">
														<div className="flex items-start gap-3 flex-1 min-w-0">
															<div className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center flex-shrink-0">
																<Stethoscope className="h-5 w-5 text-gray-600" />
															</div>
															<div className="flex-1 min-w-0">
																<div className="flex flex-col sm:flex-row sm:items-center gap-2 mb-1">
																	<h3 className="font-semibold text-gray-900 text-sm sm:text-base truncate">Dr. {consultation.doctor.name}</h3>
																	<span className={`px-2 py-1 text-xs rounded-full border ${getStatusColor(consultation.status)} self-start sm:self-auto`}>
																		{getStatusText(consultation.status)}
																	</span>
																</div>
																<p className="text-sm text-gray-600 mb-2">{consultation.doctor.specialty}</p>
																<div className="flex flex-col sm:flex-row gap-2 sm:gap-4 text-xs sm:text-sm text-gray-500">
																	<span className="flex items-center gap-1">
																		<Calendar className="h-4 w-4" />
																		{new Date(consultation.scheduledAt).toLocaleDateString('pt-BR')}
																	</span>
																	<span className="flex items-center gap-1">
																		<Clock className="h-4 w-4" />
																		{new Date(consultation.scheduledAt).toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' })}
																	</span>
																	<span className="flex items-center gap-1">
																		{getConsultationTypeIcon(consultation.appointmentType || "TELEMEDICINE")}
																		{getConsultationTypeText(consultation.appointmentType || "TELEMEDICINE")}
																	</span>
																	{consultation.amount > 0 && (
																		<span className="flex items-center gap-1">
																			<Calendar className="h-4 w-4" />
																			R$ {consultation.amount.toFixed(2)}
																		</span>
																	)}
																</div>
															</div>
														</div>
													</div>
													<div className="flex flex-col gap-3 pt-3 border-t">
														<AppointmentDetailsSheet
															appointment={consultation}
															onJoinConsultation={handleJoinConsultation}
															onCancelAppointment={handleCancelAppointment}
															onRescheduleAppointment={handleRescheduleAppointment}
														>
															<Button variant="outline" className="w-full sm:w-auto py-2.5">
																Ver Detalhes
															</Button>
														</AppointmentDetailsSheet>

														<Link href="/patient/schedule" className="w-full sm:w-auto">
															<Button variant="outline" className="w-full py-2.5">
																<Plus className="h-4 w-4 mr-2" />
																Nova Consulta
															</Button>
														</Link>
													</div>
												</div>
											</CardContent>
										</Card>
									))}
							</div>
						</CardContent>
					</Card>
				)}

				{/* Loading State for Consultations */}
				{isLoading && (
					<div className="space-y-4 sm:space-y-6">
						<Card>
							<CardContent className="p-4 sm:p-6">
								<div className="flex items-center justify-between mb-4 sm:mb-6">
									<h2 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
										<Calendar className="h-5 w-5 text-blue-600" />
										Próximas Consultas
									</h2>
								</div>
								<ConsultationsListSkeleton count={2} />
							</CardContent>
						</Card>

						<Card>
							<CardContent className="p-4 sm:p-6">
								<div className="flex items-center justify-between mb-4 sm:mb-6">
									<h2 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
										<Clock className="h-5 w-5 text-gray-600" />
										Histórico
									</h2>
								</div>
								<ConsultationsListSkeleton count={3} />
							</CardContent>
						</Card>
					</div>
				)}

				{/* Empty State - Mobile Optimized */}
				{!isLoading && filteredConsultations.length === 0 && (
					<Card className="text-center py-8 sm:py-12">
						<CardContent>
							<Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
							<h3 className="text-lg font-medium text-gray-900 mb-2">
								{searchQuery || statusFilter !== "all"
									? "Nenhuma consulta encontrada"
									: "Nenhuma consulta encontrada"
								}
							</h3>
							<p className="text-gray-600 mb-6 px-4">
								{searchQuery || statusFilter !== "all"
									? "Tente ajustar os filtros ou a busca"
									: "Você ainda não possui consultas médicas agendadas"
								}
							</p>
							<Link href="/patient/schedule">
								<Button className="bg-blue-600 hover:bg-blue-700 w-full sm:w-auto">
									<Plus className="h-4 w-4 mr-2" />
									Agendar Primeira Consulta
								</Button>
							</Link>
						</CardContent>
					</Card>
				)}
			</div>
		</div>
	);
}
