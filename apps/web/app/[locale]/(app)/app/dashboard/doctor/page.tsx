'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
	Calendar,
	Clock,
	Users,
	FileText,
	Video,
	TrendingUp,
	CheckCircle,
	XCircle,
	CalendarClock,
	ArrowLeft,
	Activity,
	Target,
	Zap,
	Filter,
} from 'lucide-react';
import {
	Card,
	CardContent,
	CardDescription,
	CardFooter,
	CardHeader,
	CardTitle,
} from '@ui/components/card';
import { Avatar, AvatarFallback, AvatarImage } from '@ui/components/avatar';
import { Button } from '@ui/components/button';
import { Badge } from '@ui/components/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@ui/components/select';
import { getDoctorDashboardData } from './actions';
import { getCompleteImageUrl } from '../../../../../../lib/image-utils';
import { SignedAvatar } from '../../../../../../components/shared/signed-avatar';
import { useMediaQuery } from '@ui/hooks/use-media-query';
import { DoctorDashboardSkeleton } from './components/doctor-dashboard-skeleton';
import { AppointmentsChart } from './components/appointments-chart';
import { MetricsPieChart } from './components/metrics-pie-chart';

// Defina interfaces para os dados
interface DoctorAppointment {
	id: string;
	patientName: string;
	patientAvatar: string | null;
	time: Date;
	status: string;
	type: string;
	consultType: string;
}

interface WeekMetrics {
	total: number;
	completedCount: number;
	canceledCount: number;
	noShowCount: number;
	inProgressCount: number;
	returnsCount: number;
	firstVisitsCount: number;
	chartData: Array<{
		day: string;
		appointments: number;
		completed: number;
	}>;
	byDay: Array<{
		date: string;
		count: number;
		completed: number;
		canceled: number;
	}>;
}

interface DoctorProfile {
	id: string;
	user: {
		name: string | null;
		email: string;
		avatarUrl: string | null;
	};
	consultationPrice: number;
	consultationDuration: number;
	specialties: Array<{ name: string }>;
}

const DoctorDashboard = () => {
	const router = useRouter();
	const { isMobile } = useMediaQuery();
	const [isLoading, setIsLoading] = useState(true);
	const [timeFilter, setTimeFilter] = useState<'today' | '7days' | '30days'>('7days');
	const [doctorProfile, setDoctorProfile] = useState<DoctorProfile | null>(
		null
	);
	const [upcomingAppointments, setUpcomingAppointments] = useState<
		DoctorAppointment[]
	>([]);
	const [weekMetrics, setWeekMetrics] = useState<WeekMetrics>({
		total: 0,
		completedCount: 0,
		canceledCount: 0,
		noShowCount: 0,
		inProgressCount: 0,
		returnsCount: 0,
		firstVisitsCount: 0,
		chartData: [],
		byDay: [],
	});

	useEffect(() => {
		const fetchDoctorData = async () => {
			try {
				setIsLoading(true);

				// Buscar dados reais do banco através de server action
				const dashboardData = await getDoctorDashboardData(timeFilter);

				if (dashboardData.profile) {
					setDoctorProfile({
						id: dashboardData.profile.id,
						user: {
							name: dashboardData.profile.user.name,
							email: dashboardData.profile.user.email,
							avatarUrl: dashboardData.profile.user.avatarUrl,
						},
						consultationPrice: Number(dashboardData.profile.consultationPrice),
						consultationDuration: dashboardData.profile.consultationDuration,
						specialties: dashboardData.profile.specialties,
					});
				}

				if (dashboardData.appointments) {
					// Mapear as consultas para o formato que a UI espera
					const mappedAppointments = dashboardData.appointments.map(
						(appointment) => ({
							id: appointment.id,
							patientName: appointment.patient.user.name || 'Paciente',
							patientAvatar: appointment.patient.user.avatarUrl,
							time: new Date(appointment.scheduledAt),
							status: appointment.status,
							type: appointment.consultType,
							consultType: appointment.isReturn
								? 'Retorno'
								: 'Consulta Regular',
						})
					);

					setUpcomingAppointments(mappedAppointments);
				}

				if (dashboardData.weekMetrics) {
					setWeekMetrics({
						total: dashboardData.weekMetrics.total,
						completedCount: dashboardData.weekMetrics.completed,
						canceledCount: dashboardData.weekMetrics.canceled,
						noShowCount: dashboardData.weekMetrics.noShow,
						inProgressCount: dashboardData.weekMetrics.inProgress,
						returnsCount: dashboardData.weekMetrics.returnsCount,
						firstVisitsCount: dashboardData.weekMetrics.firstVisitsCount,
						chartData: dashboardData.weekMetrics.byDay.map((day) => ({
							day: formatDayOfWeek(day.date),
							appointments: day.count,
							completed: day.completed,
						})),
						byDay: dashboardData.weekMetrics.byDay || [],
					});
				}

				setIsLoading(false);
			} catch (error) {
				console.error('Erro ao carregar dados do médico:', error);
				setIsLoading(false);
			}
		};

		fetchDoctorData();
	}, [timeFilter]);

	// Formatar dia da semana para o gráfico
	const formatDayOfWeek = (date: string) => {
		const days = ['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb'];
		try {
			const dayDate = new Date(date);
			return days[dayDate.getDay()] || 'N/A';
		} catch {
			return 'N/A';
		}
	};

	const getStatusConfig = (status: string) => {
		switch (status) {
			case 'SCHEDULED':
				return { label: 'Agendada', color: 'bg-blue-100 text-blue-800' };
			case 'IN_PROGRESS':
				return {
					label: 'Em Andamento',
					color: 'bg-yellow-100 text-yellow-800',
				};
			case 'COMPLETED':
				return { label: 'Finalizada', color: 'bg-green-100 text-green-800' };
			case 'CANCELED':
				return { label: 'Cancelada', color: 'bg-red-100 text-red-800' };
			case 'NO_SHOW':
				return { label: 'Não Compareceu', color: 'bg-gray-100 text-gray-800' };
			default:
				return { label: status, color: 'bg-gray-100 text-gray-800' };
		}
	};

	const formatTime = (date: Date) => {
		return (
			date.getHours().toString().padStart(2, '0') +
			':' +
			date.getMinutes().toString().padStart(2, '0')
		);
	};

	const formatDate = (date: Date) => {
		return (
			date.getDate().toString().padStart(2, '0') +
			'/' +
			(date.getMonth() + 1).toString().padStart(2, '0') +
			'/' +
			date.getFullYear()
		);
	};

	const formatDateWithTime = (date: Date) => {
		return formatDate(date) + ' ' + formatTime(date);
	};

	// Função para verificar se a data é hoje
	const isToday = (date: Date) => {
		const today = new Date();
		return (
			date.getDate() === today.getDate() &&
			date.getMonth() === today.getMonth() &&
			date.getFullYear() === today.getFullYear()
		);
	};

	// Calcular quantas consultas são para hoje
	const appointmentsToday = upcomingAppointments.filter((app) =>
		isToday(app.time)
	).length;

	const today = new Date();
	const dayOfWeek = [
		'Domingo',
		'Segunda-feira',
		'Terça-feira',
		'Quarta-feira',
		'Quinta-feira',
		'Sexta-feira',
		'Sábado',
	][today.getDay()];
	const months = [
		'janeiro',
		'fevereiro',
		'março',
		'abril',
		'maio',
		'junho',
		'julho',
		'agosto',
		'setembro',
		'outubro',
		'novembro',
		'dezembro',
	];
	const formattedToday = `${dayOfWeek}, ${today.getDate()} de ${
		months[today.getMonth()]
	}`;

	// Mensagem de saudação baseada na hora do dia
	const getGreeting = () => {
		const hour = today.getHours();
		if (hour < 12) return 'Bom dia';
		if (hour < 18) return 'Boa tarde';
		return 'Boa noite';
	};

	if (isLoading) {
		return <DoctorDashboardSkeleton />;
	}

	return (
		<div className='container mx-auto p-4 space-y-6'>
			{/* Header - Adaptado para mobile */}
			<div
				className={`${
					isMobile ? 'flex-col space-y-3' : 'flex justify-between'
				} items-center`}
			>
				<div className={`${isMobile ? 'w-full' : ''}`}>
					<h1 className='text-2xl font-bold'>
						{getGreeting()}, {doctorProfile?.user?.name?.split(' ')[0] || 'Dr.'}
					</h1>
					<p className='text-muted-foreground'>{formattedToday}</p>
				</div>
				<div className={`${isMobile ? 'w-full' : 'flex items-center space-x-2'}`}>
					<Filter className='h-4 w-4 text-muted-foreground' />
					<Select value={timeFilter} onValueChange={(value: 'today' | '7days' | '30days') => setTimeFilter(value)}>
						<SelectTrigger className={`${isMobile ? 'w-full' : 'w-32'}`}>
							<SelectValue placeholder="Período" />
						</SelectTrigger>
						<SelectContent>
							<SelectItem value="today">Hoje</SelectItem>
							<SelectItem value="7days">7 dias</SelectItem>
							<SelectItem value="30days">30 dias</SelectItem>
						</SelectContent>
					</Select>
				</div>
			</div>

			{/* Quick Stats Row - Melhorado com animações e cores */}
			<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4'>
				<Card className='hover:shadow-lg transition-all duration-300 border-l-4 border-l-blue-500'>
					<CardContent className='p-6'>
						<div className='flex items-center justify-between'>
							<div>
								<p className='text-sm font-medium text-muted-foreground mb-1'>
									Consultas Hoje
								</p>
								<h3 className='text-3xl font-bold text-blue-600'>{appointmentsToday}</h3>
								<div className='flex items-center mt-2 text-xs text-muted-foreground'>
									<Activity className='h-3 w-3 mr-1' />
									Próximas: {upcomingAppointments.length} agendadas
								</div>
							</div>
							<div className='h-12 w-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg'>
								<Calendar className='h-6 w-6 text-white' />
							</div>
						</div>
					</CardContent>
				</Card>

				<Card className='hover:shadow-lg transition-all duration-300 border-l-4 border-l-green-500'>
					<CardContent className='p-6'>
						<div className='flex items-center justify-between'>
							<div>
								<p className='text-sm font-medium text-muted-foreground mb-1'>
									Consultas na Semana
								</p>
								<h3 className='text-3xl font-bold text-green-600'>{weekMetrics.total}</h3>
								<div className='flex items-center mt-2 text-xs text-muted-foreground'>
									<Target className='h-3 w-3 mr-1' />
									{weekMetrics.completedCount} realizadas, {weekMetrics.canceledCount} canceladas
								</div>
							</div>
							<div className='h-12 w-12 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center shadow-lg'>
								<Users className='h-6 w-6 text-white' />
							</div>
						</div>
					</CardContent>
				</Card>

				<Card className='hover:shadow-lg transition-all duration-300 border-l-4 border-l-purple-500'>
					<CardContent className='p-6'>
						<div className='flex items-center justify-between'>
							<div>
								<p className='text-sm font-medium text-muted-foreground mb-1'>
									Taxa de Conclusão
								</p>
								<h3 className='text-3xl font-bold text-purple-600'>
									{weekMetrics.total
										? Math.round(
												(weekMetrics.completedCount / weekMetrics.total) * 100
										  ) + '%'
										: '0%'}
								</h3>
								<div className='flex items-center mt-2 text-xs text-muted-foreground'>
									<TrendingUp className='h-3 w-3 mr-1' />
									Não compareceram: {weekMetrics.noShowCount} pacientes
								</div>
							</div>
							<div className='h-12 w-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg'>
								<CheckCircle className='h-6 w-6 text-white' />
							</div>
						</div>
					</CardContent>
				</Card>

				<Card className='hover:shadow-lg transition-all duration-300 border-l-4 border-l-orange-500'>
					<CardContent className='p-6'>
						<div className='flex items-center justify-between'>
							<div>
								<p className='text-sm font-medium text-muted-foreground mb-1'>
									Proporção de Retornos
								</p>
								<h3 className='text-3xl font-bold text-orange-600'>
									{weekMetrics.total
										? Math.round(
												(weekMetrics.returnsCount / weekMetrics.total) * 100
										  ) + '%'
										: '0%'}
								</h3>
								<div className='flex items-center mt-2 text-xs text-muted-foreground'>
									<Zap className='h-3 w-3 mr-1' />
									{weekMetrics.returnsCount} retornos, {weekMetrics.firstVisitsCount} primeiras consultas
								</div>
							</div>
							<div className='h-12 w-12 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center shadow-lg'>
								<FileText className='h-6 w-6 text-white' />
							</div>
						</div>
					</CardContent>
				</Card>
			</div>

			{/* Main Content - Melhorado com novos charts */}
			<div className='grid grid-cols-1 lg:grid-cols-2 gap-6'>
				{/* Próximas Consultas - Melhorado */}
				<Card className='hover:shadow-lg transition-all duration-300'>
					<CardHeader className='pb-4'>
						<div className='flex items-center justify-between'>
							<div>
								<CardTitle className='text-xl'>Próximas Consultas</CardTitle>
								<CardDescription>Suas próximas consultas agendadas</CardDescription>
							</div>
							<Badge variant='outline' className='text-xs'>
								{upcomingAppointments.length} agendadas
							</Badge>
						</div>
					</CardHeader>
					<CardContent>
						{upcomingAppointments.length === 0 ? (
							<div className='text-center py-12'>
								<Calendar className='h-12 w-12 text-muted-foreground mx-auto mb-4' />
								<p className='text-muted-foreground text-lg font-medium'>Nenhuma consulta agendada</p>
								<p className='text-sm text-muted-foreground mt-2'>Suas próximas consultas aparecerão aqui</p>
							</div>
						) : (
							<div className='space-y-3'>
								{upcomingAppointments.map((appointment) => (
									<div
										key={appointment.id}
										className='group flex items-center justify-between p-4 border rounded-xl hover:bg-muted/50 hover:shadow-md transition-all duration-200 cursor-pointer'
										onClick={() =>
											router.push(`/app/appointments/${appointment.id}`)
										}
									>
										<div className='flex items-center space-x-4'>
											<SignedAvatar
												imagePath={appointment.patientAvatar}
												name={appointment.patientName}
												className='h-12 w-12 ring-2 ring-primary/20'
											/>
											<div>
												<p className='font-semibold text-base'>{appointment.patientName}</p>
												<div className='flex items-center text-sm text-muted-foreground mt-1'>
													<Clock className='h-3 w-3 mr-1' />
													{isToday(appointment.time)
														? `Hoje às ${formatTime(appointment.time)}`
														: formatDateWithTime(appointment.time)}
													<span className='mx-2'>•</span>
													<span className='font-medium'>{appointment.consultType}</span>
												</div>
											</div>
										</div>
										<div className='flex items-center space-x-2'>
											<Badge
												className={`${getStatusConfig(appointment.status).color} font-medium`}
											>
												{getStatusConfig(appointment.status).label}
											</Badge>
											{appointment.status === 'SCHEDULED' &&
												isToday(appointment.time) && (
													<Button
														size='sm'
														className='bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white shadow-lg'
														onClick={(e) => {
															e.stopPropagation();
															router.push(`/app/zapchat?appointment=${appointment.id}`);
														}}
													>
														<Video className='h-3 w-3 mr-1' />
														Iniciar
													</Button>
												)}
										</div>
									</div>
								))}
							</div>
						)}
					</CardContent>
					<CardFooter>
						<Button
							variant='outline'
							className='w-full hover:bg-primary hover:text-white transition-colors'
							onClick={() => router.push('/app/agenda')}
						>
							<CalendarClock className='h-4 w-4 mr-2' />
							Ver toda a agenda
						</Button>
					</CardFooter>
				</Card>

				{/* Chart de Desempenho - Novo componente */}
				<AppointmentsChart
					data={weekMetrics.chartData.length > 0 ? weekMetrics.chartData.map(day => ({
						day: day.day,
						appointments: day.appointments,
						completed: day.completed,
						canceled: weekMetrics.byDay?.find(d => d.date === day.date)?.canceled || 0
					})) : []}
					isLoading={isLoading}
				/>
			</div>

			{/* Chart de Distribuição - Novo componente */}
			<div className='mt-6'>
				<MetricsPieChart
					completed={weekMetrics.completedCount}
					canceled={weekMetrics.canceledCount}
					inProgress={weekMetrics.inProgressCount}
					noShow={weekMetrics.noShowCount}
					isLoading={isLoading}
				/>
			</div>

			{/* Quick Access Buttons - Melhorado com gradientes e animações */}
			<div className='grid grid-cols-2 md:grid-cols-4 gap-4 mb-16 md:mb-0'>
				<Button
					variant='outline'
					className='h-24 flex flex-col items-center justify-center hover:shadow-lg hover:scale-105 transition-all duration-200 border-2 hover:border-blue-500 group'
					onClick={() => router.push('/app/appointments')}
				>
					<div className='h-8 w-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mb-2 group-hover:scale-110 transition-transform'>
						<CalendarClock className='h-4 w-4 text-white' />
					</div>
					<span className='font-medium text-sm'>Consultas</span>
				</Button>

				<Button
					variant='outline'
					className='h-24 flex flex-col items-center justify-center hover:shadow-lg hover:scale-105 transition-all duration-200 border-2 hover:border-green-500 group'
					onClick={() => router.push('/app/patients')}
				>
					<div className='h-8 w-8 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center mb-2 group-hover:scale-110 transition-transform'>
						<Users className='h-4 w-4 text-white' />
					</div>
					<span className='font-medium text-sm'>Pacientes</span>
				</Button>

				<Button
					variant='outline'
					className='h-24 flex flex-col items-center justify-center hover:shadow-lg hover:scale-105 transition-all duration-200 border-2 hover:border-purple-500 group'
					onClick={() => router.push('/app/prescriptions')}
				>
					<div className='h-8 w-8 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center mb-2 group-hover:scale-110 transition-transform'>
						<FileText className='h-4 w-4 text-white' />
					</div>
					<span className='font-medium text-sm'>Prescrições</span>
				</Button>

				<Button
					variant='outline'
					className='h-24 flex flex-col items-center justify-center hover:shadow-lg hover:scale-105 transition-all duration-200 border-2 hover:border-orange-500 group'
					onClick={() => router.push('/app/agenda')}
				>
					<div className='h-8 w-8 bg-gradient-to-br from-orange-500 to-orange-600 rounded-lg flex items-center justify-center mb-2 group-hover:scale-110 transition-transform'>
						<Calendar className='h-4 w-4 text-white' />
					</div>
					<span className='font-medium text-sm'>Gerenciar Agenda</span>
				</Button>
			</div>
		</div>
	);
};

export default DoctorDashboard;
