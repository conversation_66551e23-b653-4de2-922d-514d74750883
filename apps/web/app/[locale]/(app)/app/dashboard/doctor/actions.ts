'use server';

import { currentUser } from "@saas/auth/lib/current-user";
import { db } from "database";
import { redirect } from "next/navigation";

export interface DoctorDashboardData {
  profile: {
    id: string;
    user: {
      name: string | null;
      email: string;
      avatarUrl: string | null;
    };
    consultationPrice: number;
    consultationDuration: number;
    specialties: Array<{ name: string }>;
  } | null;
  appointments: Array<{
    id: string;
    patient: {
      user: {
        name: string | null;
        avatarUrl: string | null;
      }
    };
    scheduledAt: Date;
    status: string;
    consultType: string;
    isReturn: boolean;
  }>;
  weekMetrics: {
    total: number;
    completed: number;
    canceled: number;
    noShow: number;
    inProgress: number;
    returnsCount: number;
    firstVisitsCount: number;
    byDay: Array<{
      date: string;
      count: number;
      completed: number;
    }>;
  };
}

export async function getDoctorDashboardData(timeFilter: 'today' | '7days' | '30days' = '7days'): Promise<DoctorDashboardData> {
  const { user } = await currentUser();

  // Verificar se o usuário está autenticado
  if (!user) {
    redirect('/login');
  }

  try {
    // Calcular datas baseado no filtro selecionado
    const today = new Date();
    let startDate: Date;
    let endDate: Date;

    switch (timeFilter) {
      case 'today':
        startDate = new Date(today);
        startDate.setHours(0, 0, 0, 0);
        endDate = new Date(today);
        endDate.setHours(23, 59, 59, 999);
        break;
      case '7days':
        startDate = new Date(today);
        startDate.setDate(today.getDate() - 6); // Últimos 7 dias incluindo hoje
        startDate.setHours(0, 0, 0, 0);
        endDate = new Date(today);
        endDate.setHours(23, 59, 59, 999);
        break;
      case '30days':
        startDate = new Date(today);
        startDate.setDate(today.getDate() - 29); // Últimos 30 dias incluindo hoje
        startDate.setHours(0, 0, 0, 0);
        endDate = new Date(today);
        endDate.setHours(23, 59, 59, 999);
        break;
      default:
        startDate = new Date(today);
        startDate.setDate(today.getDate() - 6);
        startDate.setHours(0, 0, 0, 0);
        endDate = new Date(today);
        endDate.setHours(23, 59, 59, 999);
    }

    // Primeiro buscar o médico
    const doctor = await db.doctor.findFirst({
      where: {
        userId: user.id,
      },
      include: {
        user: {
          select: {
            name: true,
            email: true,
            avatarUrl: true,
          },
        },
        specialties: {
          select: {
            name: true,
          },
        },
      },
    });

    if (!doctor) {
      console.warn(`Usuário ${user.id} não é um médico`);
      return {
        profile: null,
        appointments: [],
        weekMetrics: {
          total: 0,
          completed: 0,
          canceled: 0,
          noShow: 0,
          inProgress: 0,
          returnsCount: 0,
          firstVisitsCount: 0,
          byDay: [],
        }
      };
    }

    // Agora executar as queries com o doctorId correto
    const [
      upcomingAppointments,
      weekAppointmentsStats,
      returnsCount,
      firstVisitsCount,
      weekAppointmentsByDay
    ] = await Promise.all([
      // Buscar próximas consultas (limitado a 5)
      db.appointment.findMany({
        where: {
          doctorId: doctor.id,
          scheduledAt: {
            gte: new Date(),
          },
          status: {
            in: ['SCHEDULED', 'IN_PROGRESS'],
          },
        },
        include: {
          patient: {
            include: {
              user: {
                select: {
                  name: true,
                  avatarUrl: true,
                },
              },
            },
          },
        },
        orderBy: {
          scheduledAt: 'asc',
        },
        take: 5,
      }),

      // Buscar métricas do período selecionado - agrupado por status
      db.appointment.groupBy({
        by: ['status'],
        where: {
          doctorId: doctor.id,
          scheduledAt: {
            gte: startDate,
            lte: endDate,
          },
        },
        _count: {
          id: true,
        },
      }),

      // Buscar contagem de retornos do período
      db.appointment.count({
        where: {
          doctorId: doctor.id,
          scheduledAt: {
            gte: startDate,
            lte: endDate,
          },
          isReturn: true,
        },
      }),

      // Buscar contagem de primeiras consultas do período
      db.appointment.count({
        where: {
          doctorId: doctor.id,
          scheduledAt: {
            gte: startDate,
            lte: endDate,
          },
          isReturn: false,
        },
      }),

      // Buscar dados por dia do período em uma única query
      db.appointment.findMany({
        where: {
          doctorId: doctor.id,
          scheduledAt: {
            gte: startDate,
            lte: endDate,
          },
        },
        select: {
          scheduledAt: true,
          status: true,
        },
      })
    ]);

    // Logs de debug
    console.log('Doctor encontrado:', doctor.id);
    console.log('Consultas encontradas:', upcomingAppointments.length);
    console.log('Stats da semana:', weekAppointmentsStats);
    console.log('Consultas por dia:', weekAppointmentsByDay.length);

    // Converter dados decimais para números para evitar erro de serialização
    const serializedAppointments = upcomingAppointments.map(appointment => ({
      ...appointment,
      // Converter Decimal para Number antes de serializar
      amount: appointment.amount ? Number(appointment.amount) : 0
    }));

    // Calcular totais por status
    const totalAppointments = weekAppointmentsStats.reduce((sum, stat) => sum + stat._count.id, 0);
    const completedAppointments = weekAppointmentsStats.find(s => s.status === 'COMPLETED')?._count.id || 0;
    const canceledAppointments = weekAppointmentsStats.find(s => s.status === 'CANCELED')?._count.id || 0;
    const noShowAppointments = weekAppointmentsStats.find(s => s.status === 'NO_SHOW')?._count.id || 0;
    const inProgressAppointments = weekAppointmentsStats.find(s => s.status === 'IN_PROGRESS')?._count.id || 0;

    // Processar dados por dia do período
    const byDayData: Array<{ date: string; count: number; completed: number; canceled: number }> = [];

    const daysToProcess = timeFilter === 'today' ? 1 : timeFilter === '7days' ? 7 : 30;

    for (let i = 0; i < daysToProcess; i++) {
      const date = new Date(startDate);
      date.setDate(startDate.getDate() + i);

      const nextDay = new Date(date);
      nextDay.setDate(date.getDate() + 1);

      // Filtrar consultas do dia
      const dayAppointments = weekAppointmentsByDay.filter(apt => {
        const aptDate = new Date(apt.scheduledAt);
        return aptDate >= date && aptDate < nextDay;
      });

      const dayCompleted = dayAppointments.filter(apt => apt.status === 'COMPLETED').length;
      const dayCanceled = dayAppointments.filter(apt => apt.status === 'CANCELED').length;

      byDayData.push({
        date: date.toISOString(),
        count: dayAppointments.length,
        completed: dayCompleted,
        canceled: dayCanceled,
      });
    }

    // Formatar o preço da consulta para número
    const consultationPrice = doctor.consultationPrice
      ? Number(doctor.consultationPrice)
      : 0;

    return {
      profile: {
        id: doctor.id,
        user: {
          name: doctor.user.name,
          email: doctor.user.email,
          avatarUrl: doctor.user.avatarUrl,
        },
        consultationPrice: consultationPrice,
        consultationDuration: doctor.consultationDuration,
        specialties: doctor.specialties,
      },
      appointments: serializedAppointments,
      weekMetrics: {
        total: totalAppointments,
        completed: completedAppointments,
        canceled: canceledAppointments,
        noShow: noShowAppointments,
        inProgress: inProgressAppointments,
        returnsCount: returnsCount,
        firstVisitsCount: firstVisitsCount,
        byDay: byDayData,
      },
    };
  } catch (error) {
    console.error('Erro ao buscar dados do dashboard:', error);
    throw new Error('Falha ao carregar dados do dashboard');
  }
}
