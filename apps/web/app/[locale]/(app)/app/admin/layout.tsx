import { redirect } from "@i18n/routing";
import { currentUser } from "@saas/auth/lib/current-user";
import { UserRoleSchema } from "database";
import { UsersIcon, MessageSquare, Database, RefreshCw, LayoutDashboard, Settings, CreditCard, Wifi, Banknote } from "lucide-react";
import { getLocale, getTranslations } from "next-intl/server";
import type { PropsWithChildren } from "react";
import { redirect as nextNavigationRedirect } from "next/navigation";
import Link from "next/link";
import { cn } from "@ui/lib";

interface AdminMenuItemProps {
	href: string;
	title: string;
	icon?: React.ReactNode;
	isActive?: boolean;
}

function AdminMenuItem({ href, title, icon, isActive }: AdminMenuItemProps) {
	return (
		<Link
			href={href}
			className={cn(
				"flex items-center gap-3 rounded-md px-3 py-2 text-sm transition-all hover:bg-muted",
				isActive ? "bg-muted font-medium text-foreground" : "text-muted-foreground"
			)}
		>
			{icon && <div className="w-4">{icon}</div>}
			<span>{title}</span>
		</Link>
	);
}

interface AdminMenuProps {
	items: {
		title: string;
		href: string;
		icon: React.ReactNode;
	}[];
	currentPath: string;
}

function AdminMenu({ items, currentPath }: AdminMenuProps) {
	return (
		<div className="flex flex-col gap-1 px-2">
			{items.map((item) => (
				<AdminMenuItem
					key={item.href}
					href={item.href}
					title={item.title}
					icon={item.icon}
					isActive={currentPath.includes(item.href)}
				/>
			))}
		</div>
	);
}

export default async function AdminLayout({ children }: PropsWithChildren) {
	const locale = await getLocale();
	const t = await getTranslations();
	const session = await currentUser();
	const pathname = "/app/admin"; // This is a simplification - ideally you'd get the actual pathname

	if (!session?.user || session.user.role !== UserRoleSchema.Values.ADMIN) {
		nextNavigationRedirect("/app/dashboard");
	}

	const menuItems = [
		// {
		// 	title: "Dashboard",
		// 	href: "/app/admin",
		// 	icon: <LayoutDashboard className="h-4 w-4" />,
		// },
		{
			title: "Financeiro",
			href: "/app/finance",
			icon: <Banknote className="h-4 w-4" />,
		},
		{
			title: "Assinaturas",
			href: "/app/admin/subscriptions",
			icon: <CreditCard className="h-4 w-4" />,
		},
		{
			title: t("admin.menu.users") || "Usuários",
			href: "/app/admin/users",
			icon: <UsersIcon className="h-4 w-4" />,
		},

	];

	return (
		<div className="container max-w-7xl py-6">
			<div className="mb-4">
				<h1 className="text-2xl font-bold flex items-center gap-2">
					<Settings className="h-5 w-5" />
					Administração
				</h1>
				<p className="text-sm text-muted-foreground">
					Gerencie configurações do sistema e usuários
				</p>
			</div>

			<div className="flex flex-col md:flex-row gap-6">
				<div className="w-full md:w-[240px] shrink-0">
					<div className="sticky top-4">
						<div className="rounded-lg border bg-card text-card-foreground shadow">
							<div className="p-3 flex items-center border-b">
								<span className="text-sm font-medium">Menu de Administração</span>
							</div>
							<div className="py-2">
								<AdminMenu items={menuItems} currentPath={pathname} />
							</div>
						</div>
					</div>
				</div>

				<div className="flex-1">

						{children}

				</div>
			</div>
		</div>
	);
}
