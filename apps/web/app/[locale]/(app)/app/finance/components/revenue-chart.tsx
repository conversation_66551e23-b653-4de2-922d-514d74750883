'use client';

import { Line<PERSON><PERSON>, Line, XAxis, <PERSON>A<PERSON>s, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { format, parseISO } from 'date-fns';
import { ptBR } from 'date-fns/locale';

interface RevenueChartData {
  date: string;
  revenue: number;
  appointments?: number;
  platformFee?: number;
  doctorRevenue?: number;
}

interface RevenueChartProps {
  data: RevenueChartData[];
  showAppointments?: boolean;
  showPlatformFee?: boolean;
  showDoctorRevenue?: boolean;
  height?: number;
}

export function RevenueChart({ 
  data, 
  showAppointments = false, 
  showPlatformFee = false,
  showDoctorRevenue = false,
  height = 300 
}: RevenueChartProps) {
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  const formatDate = (dateString: string) => {
    try {
      const date = parseISO(dateString);
      return format(date, 'dd/MM', { locale: ptBR });
    } catch {
      return dateString;
    }
  };

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-background border border-border rounded-lg p-3 shadow-lg">
          <p className="font-medium mb-2">
            {format(parseISO(label), 'dd/MM/yyyy', { locale: ptBR })}
          </p>
          {payload.map((entry: any, index: number) => (
            <p key={index} className="text-sm" style={{ color: entry.color }}>
              {entry.name}: {
                entry.dataKey === 'appointments' 
                  ? entry.value 
                  : formatCurrency(entry.value)
              }
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  if (!data || data.length === 0) {
    return (
      <div className="flex items-center justify-center h-[300px] text-muted-foreground">
        Nenhum dado disponível para o período selecionado
      </div>
    );
  }

  return (
    <ResponsiveContainer width="100%" height={height}>
      <LineChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
        <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
        <XAxis 
          dataKey="date" 
          tickFormatter={formatDate}
          className="text-xs"
        />
        <YAxis 
          tickFormatter={formatCurrency}
          className="text-xs"
        />
        <Tooltip content={<CustomTooltip />} />
        
        <Line
          type="monotone"
          dataKey="revenue"
          stroke="#10b981"
          strokeWidth={2}
          dot={{ fill: '#10b981', strokeWidth: 2, r: 4 }}
          activeDot={{ r: 6 }}
          name="Receita"
        />
        
        {showPlatformFee && (
          <Line
            type="monotone"
            dataKey="platformFee"
            stroke="#f59e0b"
            strokeWidth={2}
            dot={{ fill: '#f59e0b', strokeWidth: 2, r: 4 }}
            name="Taxa da Plataforma"
          />
        )}
        
        {showDoctorRevenue && (
          <Line
            type="monotone"
            dataKey="doctorRevenue"
            stroke="#3b82f6"
            strokeWidth={2}
            dot={{ fill: '#3b82f6', strokeWidth: 2, r: 4 }}
            name="Receita dos Médicos"
          />
        )}
        
        {showAppointments && (
          <Line
            type="monotone"
            dataKey="appointments"
            stroke="#8b5cf6"
            strokeWidth={2}
            dot={{ fill: '#8b5cf6', strokeWidth: 2, r: 4 }}
            name="Consultas"
            yAxisId="right"
          />
        )}
      </LineChart>
    </ResponsiveContainer>
  );
}
