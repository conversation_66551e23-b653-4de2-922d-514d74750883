'use client';

import { useState } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@ui/components/table";
import { Badge } from "@ui/components/badge";
import { Button } from "@ui/components/button";
import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { format, parseISO } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { PaymentStatus, PaymentMethod } from 'database';
import { ChevronLeft, ChevronRight, Eye } from 'lucide-react';

interface Transaction {
  id: string;
  amount: number;
  doctorAmount: number;
  platformFee: number;
  status: PaymentStatus;
  paymentMethod: PaymentMethod;
  paidAt?: string;
  createdAt: string;
  patientName?: string;
  doctorName?: string;
  appointmentDate?: string;
}

interface TransactionsTableProps {
  transactions: Transaction[];
  showDoctorColumn?: boolean;
  showPatientColumn?: boolean;
  onViewDetails?: (transactionId: string) => void;
  itemsPerPage?: number;
}

const STATUS_LABELS = {
  [PaymentStatus.PAID]: 'Pago',
  [PaymentStatus.PENDING]: 'Pendente',
  [PaymentStatus.FAILED]: 'Falhou',
  [PaymentStatus.REFUNDED]: 'Reembolsado',
};

const STATUS_COLORS = {
  [PaymentStatus.PAID]: 'bg-green-100 text-green-800',
  [PaymentStatus.PENDING]: 'bg-yellow-100 text-yellow-800',
  [PaymentStatus.FAILED]: 'bg-red-100 text-red-800',
  [PaymentStatus.REFUNDED]: 'bg-gray-100 text-gray-800',
};

const METHOD_LABELS = {
  [PaymentMethod.CREDIT_CARD]: 'Cartão de Crédito',
  [PaymentMethod.PIX]: 'PIX',
  [PaymentMethod.BOLETO]: 'Boleto',
  [PaymentMethod.DEBIT_CARD]: 'Cartão de Débito',
};

export function TransactionsTable({
  transactions,
  showDoctorColumn = false,
  showPatientColumn = true,
  onViewDetails,
  itemsPerPage = 10,
}: TransactionsTableProps) {
  const [currentPage, setCurrentPage] = useState(1);

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(value);
  };

  const formatDate = (dateString: string) => {
    try {
      const date = parseISO(dateString);
      return format(date, 'dd/MM/yyyy HH:mm', { locale: ptBR });
    } catch {
      return dateString;
    }
  };

  // Paginação
  const totalPages = Math.ceil(transactions.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentTransactions = transactions.slice(startIndex, endIndex);

  const goToPage = (page: number) => {
    setCurrentPage(Math.max(1, Math.min(page, totalPages)));
  };

  if (!transactions || transactions.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Transações</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center text-muted-foreground py-8">
            Nenhuma transação encontrada para os filtros selecionados
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>
          Transações ({transactions.length})
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Data</TableHead>
                {showPatientColumn && <TableHead>Paciente</TableHead>}
                {showDoctorColumn && <TableHead>Médico</TableHead>}
                <TableHead>Valor</TableHead>
                <TableHead>Método</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Consulta</TableHead>
                {onViewDetails && <TableHead className="w-[100px]">Ações</TableHead>}
              </TableRow>
            </TableHeader>
            <TableBody>
              {currentTransactions.map((transaction) => (
                <TableRow key={transaction.id}>
                  <TableCell className="font-medium">
                    {formatDate(transaction.createdAt)}
                  </TableCell>
                  
                  {showPatientColumn && (
                    <TableCell>
                      {transaction.patientName || 'N/A'}
                    </TableCell>
                  )}
                  
                  {showDoctorColumn && (
                    <TableCell>
                      {transaction.doctorName || 'N/A'}
                    </TableCell>
                  )}
                  
                  <TableCell>
                    <div className="space-y-1">
                      <div className="font-medium">
                        {formatCurrency(transaction.amount)}
                      </div>
                      {showDoctorColumn && (
                        <div className="text-xs text-muted-foreground">
                          Médico: {formatCurrency(transaction.doctorAmount)}
                        </div>
                      )}
                    </div>
                  </TableCell>
                  
                  <TableCell>
                    {METHOD_LABELS[transaction.paymentMethod]}
                  </TableCell>
                  
                  <TableCell>
                    <Badge 
                      variant="secondary" 
                      className={STATUS_COLORS[transaction.status]}
                    >
                      {STATUS_LABELS[transaction.status]}
                    </Badge>
                  </TableCell>
                  
                  <TableCell>
                    {transaction.appointmentDate ? (
                      <div className="text-sm">
                        {format(parseISO(transaction.appointmentDate), 'dd/MM/yyyy HH:mm', { locale: ptBR })}
                      </div>
                    ) : (
                      <span className="text-muted-foreground">N/A</span>
                    )}
                  </TableCell>
                  
                  {onViewDetails && (
                    <TableCell>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onViewDetails(transaction.id)}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  )}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>

        {/* Paginação */}
        {totalPages > 1 && (
          <div className="flex items-center justify-between space-x-2 py-4">
            <div className="text-sm text-muted-foreground">
              Mostrando {startIndex + 1} a {Math.min(endIndex, transactions.length)} de {transactions.length} transações
            </div>
            
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => goToPage(currentPage - 1)}
                disabled={currentPage === 1}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              
              <div className="flex items-center space-x-1">
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  const page = i + 1;
                  return (
                    <Button
                      key={page}
                      variant={currentPage === page ? "default" : "outline"}
                      size="sm"
                      onClick={() => goToPage(page)}
                    >
                      {page}
                    </Button>
                  );
                })}
              </div>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => goToPage(currentPage + 1)}
                disabled={currentPage === totalPages}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
