'use client';

import { <PERSON>, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { LucideIcon } from "lucide-react";
import { cn } from "@ui/lib";

interface FinancialMetricsCardProps {
  title: string;
  value: string | number;
  icon: LucideIcon;
  trend?: string;
  trendDirection?: 'up' | 'down' | 'neutral';
  description?: string;
  className?: string;
}

export function FinancialMetricsCard({
  title,
  value,
  icon: Icon,
  trend,
  trendDirection = 'neutral',
  description,
  className,
}: FinancialMetricsCardProps) {
  const formatValue = (val: string | number) => {
    if (typeof val === 'number') {
      // Se for um valor monetário (maior que 1000), formatar como moeda
      if (val >= 1000) {
        return new Intl.NumberFormat('pt-BR', {
          style: 'currency',
          currency: 'BRL',
        }).format(val);
      }
      // Se for um percentual (menor que 100 e tem trend), adicionar %
      if (val < 100 && trend) {
        return `${val}%`;
      }
      return val.toString();
    }
    return val;
  };

  const getTrendColor = () => {
    switch (trendDirection) {
      case 'up':
        return 'text-green-600';
      case 'down':
        return 'text-red-600';
      default:
        return 'text-muted-foreground';
    }
  };

  return (
    <Card className={cn("", className)}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <Icon className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{formatValue(value)}</div>
        {(trend || description) && (
          <p className={cn("text-xs", getTrendColor())}>
            {trend && (
              <span className="inline-flex items-center">
                {trendDirection === 'up' && '↗ '}
                {trendDirection === 'down' && '↘ '}
                {trend}
              </span>
            )}
            {trend && description && ' • '}
            {description && (
              <span className="text-muted-foreground">{description}</span>
            )}
          </p>
        )}
      </CardContent>
    </Card>
  );
}
