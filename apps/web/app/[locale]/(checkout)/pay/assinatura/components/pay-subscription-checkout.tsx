'use client';

import { useState } from 'react';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm, FormProvider } from 'react-hook-form';
import { Card, CardContent, CardHeader, CardTitle } from '@ui/components/card';
import { Button } from '@ui/components/button';
import { Badge } from '@ui/components/badge';
import { Input } from '@ui/components/input';
import { Label } from '@ui/components/label';
import { useToast } from '@ui/hooks/use-toast';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { CreditCard as CreditCardIcon, Shield, Clock, ArrowLeft, Loader2, Check, CheckCircle2 } from 'lucide-react';
import { BRPhoneInput } from '../../../checkout/components/phone-input';
import { CPFInput } from '../../../checkout/components/cpf-input';

const subscriptionFormSchema = z.object({
  customerData: z.object({
    name: z.string().min(2, 'Nome deve ter pelo menos 2 caracteres'),
    email: z.string().email('Email inválido'),
    phone: z.string().min(10, 'Telefone inválido'),
    cpf: z.string().min(11, 'CPF inválido'),
  }),
  paymentMethod: z.enum(['CREDIT_CARD', 'PIX', 'BOLETO']).default('CREDIT_CARD'),
  creditCard: z
    .object({
      cardNumber: z.string(),
      cardHolder: z.string(),
      cardExpiry: z.string(),
      cardCvv: z.string(),
    })
    .optional(),
  acceptTerms: z.boolean().refine((v) => v === true, 'Você deve aceitar os termos'),
}).refine(
  (data) => {
    if (data.paymentMethod === 'CREDIT_CARD') {
      return (
        data.creditCard &&
        data.creditCard.cardNumber.length >= 13 &&
        data.creditCard.cardHolder.length >= 2 &&
        data.creditCard.cardExpiry.length >= 5 &&
        data.creditCard.cardCvv.length >= 3
      );
    }
    return true;
  },
  { message: 'Dados do cartão de crédito são obrigatórios', path: ['creditCard'] }
);

type SubscriptionFormData = z.infer<typeof subscriptionFormSchema>;

const PLANS = {
  'zapvida-sempre': {
    id: 'zapvida-sempre',
    name: 'ZapVida Sempre',
    price: 49.0,
    originalPrice: 160.0,
    consultationsIncluded: 2,
    description: '2 consultas médicas por mês',
    cycle: 'MONTHLY' as const,
    features: [
      '2 consultas médicas por mês',
      'Atendimento por vídeo, áudio ou chat',
      'Acesso a especialistas qualificados',
      'Receitas e atestados digitais válidos',
      'Histórico médico completo',
      'Suporte prioritário 24/7',
      'Cancelamento a qualquer momento',
    ],
  },
};

export function PaySubscriptionCheckout() {
  const router = useRouter();
  const { toast } = useToast();
  const searchParams = useSearchParams();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const selectedPlanId = (searchParams?.get('plan') || 'zapvida-sempre') as keyof typeof PLANS;
  const plan = PLANS[selectedPlanId];

  const methods = useForm<SubscriptionFormData>({
    resolver: zodResolver(subscriptionFormSchema),
    defaultValues: {
      customerData: { name: '', email: '', phone: '', cpf: '' },
      paymentMethod: 'CREDIT_CARD',
      creditCard: { cardNumber: '', cardHolder: '', cardExpiry: '', cardCvv: '' },
      acceptTerms: false,
    },
    mode: 'onChange',
  });

  async function onSubmit(data: SubscriptionFormData) {
    if (!plan) return;
    setIsSubmitting(true);
    try {
      toast({ title: 'Processando assinatura', description: 'Aguarde…' });
      const res = await fetch('/api/pay/subscription', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          planId: plan.id,
          paymentMethod: data.paymentMethod,
          customerData: data.customerData,
          ...(data.paymentMethod === 'CREDIT_CARD' && data.creditCard ? { creditCard: data.creditCard } : {}),
        }),
      });
      if (!res.ok) {
        const j = await res.json().catch(() => ({}));
        throw new Error(j?.message || 'Falha ao criar assinatura');
      }
      const payload = await res.json();
      toast({ title: 'Assinatura criada com sucesso!' });
      // Para PIX recorrente via API: se veio pixCode/paymentId, navegar para página de PIX
      if (data.paymentMethod === 'PIX' && payload?.pixCode && payload?.paymentId) {
        router.push(`/checkout/pix?paymentId=${encodeURIComponent(payload.paymentId)}`);
        return;
      }
      const params = new URLSearchParams({
        subscriptionId: payload?.subscriptionId || '',
        planName: PLANS[selectedPlanId].name,
        amount: String(PLANS[selectedPlanId].price)
      });

      if (payload?.magicUrl) params.append('magicUrl', payload.magicUrl);
      if (payload?.paymentStatus) params.append('paymentStatus', payload.paymentStatus);
      if (payload?.paymentCharged !== undefined) params.append('paymentCharged', String(payload.paymentCharged));

      const successUrl = `/checkout/success/subscription?${params.toString()}`;
      router.push(successUrl);
    } catch (e: any) {
      toast({ title: 'Erro ao processar assinatura', description: e?.message || 'Tente novamente.' });
    } finally {
      setIsSubmitting(false);
    }
  }

  if (!plan) {
    return null;
  }

  const {
    register,
    setValue,
    watch,
    formState: { errors },
    handleSubmit,
  } = methods;

  const selectedPaymentMethod = watch('paymentMethod');

  return (
    <div className='min-h-screen bg-gray-50'>
      <div className='container mx-auto px-4 py-8'>
        <div className='max-w-6xl mx-auto'>
          <div className='mb-8'>
            <Link href='/assinaturas' className='inline-flex items-center text-sm text-muted-foreground hover:text-foreground mb-4'>
              <ArrowLeft className='mr-2 h-4 w-4' /> Voltar para planos
            </Link>
            <div className='text-center'>
              <h1 className='text-2xl font-bold text-foreground mb-2'>Finalizar Assinatura</h1>
              <p className='text-muted-foreground'>Complete sua assinatura e comece a cuidar da sua saúde hoje mesmo</p>
            </div>
          </div>

          <FormProvider {...methods}>
            <form onSubmit={handleSubmit(onSubmit)}>
              <div className='grid grid-cols-1 lg:grid-cols-2 gap-8'>
                <Card className='border border-border h-fit lg:sticky lg:top-6'>
                  <CardHeader className='pb-4'>
                    <div className='flex items-center justify-between'>
                      <CardTitle className='text-lg font-semibold'>{plan.name}</CardTitle>
                      <Badge className='bg-primary/10 text-primary'>Mais popular</Badge>
                    </div>
                  </CardHeader>
                  <CardContent className='space-y-5'>
                    <div className='text-center py-4 bg-muted/50 rounded-lg'>
                      <div className='flex items-center justify-center gap-2 mb-2'>
                        <span className='text-2xl font-bold text-primary'>R$ {plan.price.toFixed(2)}</span>
                        <span className='text-muted-foreground'>/mês</span>
                      </div>
                      <div className='flex items-center justify-center gap-2'>
                        <span className='text-muted-foreground line-through text-sm'>R$ {plan.originalPrice.toFixed(2)}</span>
                        <span className='text-green-600 font-semibold text-xs'>
                          Economize R$ {(plan.originalPrice - plan.price).toFixed(2)}
                        </span>
                      </div>
                    </div>
                    <div className='bg-primary/5 rounded-lg p-4 border border-primary/10'>
                      <h4 className='text-sm font-semibold text-primary mb-3 flex items-center gap-1.5'>
                        <Check className='h-4 w-4' /> Incluso na Assinatura
                      </h4>
                      <div className='space-y-3'>
                        {plan.features.map((feature, idx) => (
                          <div key={idx} className='flex items-center gap-2'>
                            <CheckCircle2 className='h-4 w-4 text-green-500 flex-shrink-0' />
                            <span className='text-sm text-foreground'>{feature}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                    <div className='space-y-2 pt-2 border-t'>
                      <div className='flex items-center text-sm text-muted-foreground'>
                        <Shield className='h-4 w-4 mr-2 text-green-500' /> <span>Pagamento 100% seguro</span>
                      </div>
                      <div className='flex items-center text-sm text-muted-foreground'>
                        <Clock className='h-4 w-4 mr-2 text-green-500' /> <span>Ativação imediata</span>
                      </div>
                      <div className='flex items-center text-sm text-muted-foreground'>
                        <CreditCardIcon className='h-4 w-4 mr-2 text-green-500' /> <span>Cancele quando quiser</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <div className='space-y-6'>
                  <Card>
                    <CardHeader>
                      <CardTitle className='text-lg font-semibold'>Dados Pessoais</CardTitle>
                    </CardHeader>
                    <CardContent className='space-y-4'>
                      <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                        <div className='space-y-2'>
                          <Label htmlFor='name'>Nome completo *</Label>
                          <Input id='name' placeholder='Seu nome completo' {...register('customerData.name')} className={errors.customerData?.name ? 'border-red-500' : ''} />
                        </div>
                        <div className='space-y-2'>
                          <Label htmlFor='email'>Email *</Label>
                          <Input id='email' type='email' placeholder='<EMAIL>' {...register('customerData.email')} className={errors.customerData?.email ? 'border-red-500' : ''} />
                        </div>
                      </div>
                      <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                        <div className='space-y-2'>
                          <Label htmlFor='phone'>Telefone *</Label>
                          <BRPhoneInput value={watch('customerData.phone')} onChange={(v) => setValue('customerData.phone', v)} className={errors.customerData?.phone ? 'border-red-500' : ''} />
                        </div>
                        <div className='space-y-2'>
                          <Label htmlFor='cpf'>CPF *</Label>
                          <CPFInput value={watch('customerData.cpf')} onChange={(v) => setValue('customerData.cpf', v)} className={errors.customerData?.cpf ? 'border-red-500' : ''} />
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle className='text-lg font-semibold'>Método de Pagamento</CardTitle>
                      <p className='text-sm text-muted-foreground'>Método atual: {selectedPaymentMethod}</p>
                    </CardHeader>
                    <CardContent className='space-y-4'>
                      <div className='grid grid-cols-1 sm:grid-cols-3 gap-3'>
                        {(['CREDIT_CARD', 'PIX', 'BOLETO'] as const).map((method) => (
                          <div
                            key={method}
                            onClick={() => methods.setValue('paymentMethod', method, { shouldValidate: true })}
                            className={`relative flex cursor-pointer rounded-lg border p-4 transition-all ${selectedPaymentMethod === method ? 'border-primary bg-primary/5' : 'hover:border-primary/50'}`}
                          >
                            <div className='flex items-center gap-3'>
                              <div className={`flex h-10 w-10 items-center justify-center rounded-full ${selectedPaymentMethod === method ? 'bg-primary text-primary-foreground' : 'bg-muted'}`}>
                                <CreditCardIcon className='h-5 w-5' />
                              </div>
                              <span className='font-medium'>
                                {method === 'CREDIT_CARD' ? 'Cartão' : method === 'PIX' ? 'PIX' : 'Boleto'}
                              </span>
                            </div>
                          </div>
                        ))}
                      </div>

                      {selectedPaymentMethod === 'CREDIT_CARD' && (
                        <div className='grid grid-cols-1 gap-4 pt-2 border-t'>
                          <div className='space-y-2'>
                            <Label htmlFor='cardNumber'>Número do cartão *</Label>
                            <Input id='cardNumber' placeholder='•••• •••• •••• ••••' {...register('creditCard.cardNumber')} />
                          </div>
                          <div className='space-y-2'>
                            <Label htmlFor='cardHolder'>Nome no cartão *</Label>
                            <Input id='cardHolder' placeholder='Nome impresso no cartão' {...register('creditCard.cardHolder')} />
                          </div>
                          <div className='grid grid-cols-2 gap-4'>
                            <div className='space-y-2'>
                              <Label htmlFor='cardExpiry'>Validade *</Label>
                              <Input id='cardExpiry' placeholder='MM/AA' {...register('creditCard.cardExpiry')} />
                            </div>
                            <div className='space-y-2'>
                              <Label htmlFor='cardCvv'>CVV *</Label>
                              <Input id='cardCvv' placeholder='123' {...register('creditCard.cardCvv')} />
                            </div>
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className='pt-6'>
                      <div className='space-y-4'>
                        <div className='flex items-start space-x-2'>
                          <input type='checkbox' id='acceptTerms' className='mt-1' {...register('acceptTerms')} />
                          <Label htmlFor='acceptTerms' className='text-sm leading-5'>
                            Eu aceito os <Link href='/termos' className='text-primary hover:underline'>termos de uso</Link> e
                            <Link href='/privacidade' className='text-primary hover:underline'> política de privacidade</Link>.
                          </Label>
                        </div>
                        {errors.acceptTerms && <p className='text-sm text-red-500'>{errors.acceptTerms.message}</p>}

                        <Button type='submit' className='w-full' size='lg' disabled={isSubmitting}>
                          {isSubmitting ? (<><Loader2 className='mr-2 h-4 w-4 animate-spin' /> Processando...</>) : `Assinar por R$ ${plan.price.toFixed(2)}/mês`}
                        </Button>
                        <p className='text-xs text-center text-muted-foreground'>Pagamento seguro</p>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </form>
          </FormProvider>
        </div>
      </div>
    </div>
  );
}


