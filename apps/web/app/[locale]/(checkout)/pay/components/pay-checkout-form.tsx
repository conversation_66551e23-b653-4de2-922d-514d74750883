// apps/web/(checkout)/pay/components/pay-checkout-form.tsx

'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useRouter } from 'next/navigation';
import { FormProvider, useForm } from 'react-hook-form';
import { CheckoutFormData, checkoutFormSchema } from '../../checkout/components/types';
import { CustomerForm } from '../../checkout/components/customer-form';
import { PaymentForm } from '../../checkout/components/payment-form';
import { useToast } from '@ui/hooks/use-toast';
import { useState } from 'react';

interface PayCheckoutFormProps {
  product: any;
}

export function PayCheckoutForm({ product }: PayCheckoutFormProps) {
  const router = useRouter();
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const defaultPaymentMethod = product.acceptedPayments?.includes('CREDIT_CARD')
    ? 'CREDIT_CARD'
    : product.acceptedPayments?.includes('PIX')
    ? 'PIX'
    : 'CREDIT_CARD';

  const methods = useForm<CheckoutFormData>({
    resolver: zodResolver(checkoutFormSchema),
    defaultValues: {
      customerData: { name: '', email: '', phone: '', cpf: '' },
      doctorId: product.checkoutType === 'PLANTAO' ? 'plantao-service' : product.id,
      scheduledAt: product.scheduledAt,
      duration: product.duration,
      paymentMethod: defaultPaymentMethod,
      creditCard: { cardNumber: '', cardHolder: '', cardExpiry: '', cardCvv: '', installments: 1 },
      isOnDuty: product.isOnDuty,
      urgencyLevel: product.urgencyLevel,
      ...(product.partner && { partner: product.partner }),
    },
  });

  async function callApi(url: string, body: Record<string, any>) {
    const res = await fetch(url, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(body),
    });
    if (!res.ok) {
      const err = await res.json().catch(() => ({}));
      throw new Error(err?.message || 'Falha ao processar pagamento');
    }
    return res.json();
  }

  const onSubmit = async (data: CheckoutFormData) => {
    try {
      setIsSubmitting(true);

      if (!data.customerData?.name || !data.customerData?.email) {
        throw new Error('Nome e email são obrigatórios');
      }

      const safeCustomerData = {
        name: data.customerData.name || '',
        email: data.customerData.email || '',
        phone: data.customerData.phone || '',
        cpf: data.customerData.cpf || '',
      };

      const futureDateForSchedule = new Date();
      futureDateForSchedule.setDate(futureDateForSchedule.getDate() + 2);

      toast({ title: 'Processando pagamento', description: 'Aguarde…' });

      let result: any;

      if (product.checkoutType === 'SUBSCRIPTION') {
        result = await callApi('/api/pay/subscription', {
          planId: product.id,
          customerData: safeCustomerData,
          paymentMethod: data.paymentMethod,
          creditCard:
            data.paymentMethod === 'CREDIT_CARD'
              ? {
                  cardNumber: data.creditCard?.cardNumber?.replace(/\D/g, '') || '',
                  cardHolder: data.creditCard?.cardHolder || '',
                  cardExpiry: data.creditCard?.cardExpiry || '',
                  cardCvv: data.creditCard?.cardCvv || '',
                }
              : undefined,
        });
      } else if (product.checkoutType === 'PLANTAO' || product.isOnDuty) {
        // On-duty (plantão) without doctor
        result = await callApi('/api/pay/plantao', {
          customerData: safeCustomerData,
          paymentMethod: data.paymentMethod,
          creditCard:
            data.paymentMethod === 'CREDIT_CARD'
              ? {
                  cardNumber: data.creditCard?.cardNumber?.replace(/\D/g, '') || '',
                  cardHolder: data.creditCard?.cardHolder || '',
                  cardExpiry: data.creditCard?.cardExpiry || '',
                  cardCvv: data.creditCard?.cardCvv || '',
                  installments: data.creditCard?.installments || 1,
                }
              : undefined,
          urgencyLevel: product.urgencyLevel,
          partner: product.partner,
        });
      } else {
        // Scheduled with doctor
        result = await callApi('/api/pay/appointment', {
          customerData: safeCustomerData,
          doctorId: data.doctorId || product.id,
          paymentMethod: data.paymentMethod,
          scheduledAt: data.scheduledAt || futureDateForSchedule,
          duration: data.duration || undefined,
          creditCard:
            data.paymentMethod === 'CREDIT_CARD'
              ? {
                  cardNumber: data.creditCard?.cardNumber?.replace(/\D/g, '') || '',
                  cardHolder: data.creditCard?.cardHolder || '',
                  cardExpiry: data.creditCard?.cardExpiry || '',
                  cardCvv: data.creditCard?.cardCvv || '',
                  installments: data.creditCard?.installments || 1,
                }
              : undefined,
          isOnDuty: product.isOnDuty,
          urgencyLevel: product.urgencyLevel,
          ...(product.partner && { partner: product.partner }),
        });
      }

      if (product.checkoutType === 'SUBSCRIPTION') {
        if (!result?.success) throw new Error('Falha ao criar assinatura');
        toast({ title: 'Assinatura criada com sucesso!' });
        // Redireciona para página de sucesso da assinatura
        const params = new URLSearchParams({
          subscriptionId: result.subscriptionId || '',
          planName: product.title,
          amount: String(product.price)
        });

        if (result.magicUrl) params.append('magicUrl', result.magicUrl);
        if (result.paymentStatus) params.append('paymentStatus', result.paymentStatus);
        if (result.paymentCharged !== undefined) params.append('paymentCharged', String(result.paymentCharged));

        const successUrl = `/checkout/success/subscription?${params.toString()}`;
        await router.push(successUrl);
        return;
      }

      if (!result?.transactionId) throw new Error('ID da transação não retornado');

      if (data.paymentMethod === 'PIX') {
        if (!result.pixCode && result.paymentMethod === 'PIX') {
          throw new Error('Dados do PIX não foram gerados corretamente');
        }
        try {
          await router.push(`/checkout/pix?transactionId=${result.transactionId}`);
        } catch {
          window.location.href = `/checkout/pix?transactionId=${result.transactionId}`;
        }
        return;
      }

      if (product.checkoutType === 'PLANTAO') {
        toast({ title: 'Pagamento recebido!' });
        await router.push(`/checkout/success/plantao?transactionId=${encodeURIComponent(result.transactionId)}`);
        return;
      } else if (product.successUrl) {
        toast({ title: 'Pagamento processado com sucesso!' });
        setTimeout(() => {
          router.push(`${product.successUrl}?appointmentId=${result.appointmentId}`);
        }, 800);
      } else {
        toast({ title: 'Pagamento processado com sucesso!' });
        setTimeout(() => {
          router.push(`/checkout/success?appointmentId=${result.appointmentId}`);
        }, 800);
      }
    } catch (error: any) {
      toast({ title: 'Erro ao processar pagamento', description: error?.message || 'Tente novamente.' , variant: 'error' as any});
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <FormProvider {...methods}>
      <form onSubmit={methods.handleSubmit(onSubmit)} className='container max-w-6xl mx-auto px-0 lg:px-4'>
        <div className='lg:col-span-8 space-y-6'>
          <CustomerForm />
          <PaymentForm
            totalAmount={calculateTotal(product.price, product.serviceFee)}
            installmentsLimit={product.installmentsLimit}
            enableInstallments={product.enableInstallments}
            acceptedPayments={product.acceptedPayments}
            loading={isSubmitting}
            confirmCta={
              product.checkoutType === 'SUBSCRIPTION'
                ? 'Confirmar Assinatura'
                : product.checkoutType === 'PLANTAO'
                ? 'Confirmar Atendimento'
                : 'Confirmar Consulta'
            }
            pixCta={
              product.checkoutType === 'SUBSCRIPTION'
                ? 'Assinar com PIX'
                : product.checkoutType === 'PLANTAO'
                ? 'Gerar PIX do Plantão'
                : 'Gerar QR Code PIX'
            }
            onPixClick={async () => {
              const current = methods.getValues();
              setIsSubmitting(true);
              try {
                if (!current.customerData?.name || !current.customerData?.email) {
                  throw new Error('Por favor, preencha nome e email antes de continuar.');
                }
                if (product.checkoutType === 'SUBSCRIPTION') {
                  const sub = await callApi('/api/pay/subscription', {
                    planId: product.id,
                    customerData: current.customerData,
                    paymentMethod: 'PIX',
                  });
                  if (!sub?.success) throw new Error('Falha ao criar assinatura via PIX');
                  // Totalmente via API: se veio pixCode do primeiro pagamento, vá para a página de PIX
                  if (sub.pixCode && sub.paymentId) {
                    await router.push(`/checkout/pix?paymentId=${encodeURIComponent(sub.paymentId)}`);
                    return;
                  }
                  throw new Error('Não foi possível gerar o PIX da assinatura');
                  return;
                }
                let result: any;
                if (product.checkoutType === 'PLANTAO' || product.isOnDuty) {
                  result = await callApi('/api/pay/plantao', {
                    customerData: current.customerData,
                    paymentMethod: 'PIX',
                    urgencyLevel: product.urgencyLevel,
                    partner: product.partner,
                  });
                } else {
                  result = await callApi('/api/pay/appointment', {
                    customerData: current.customerData,
                    doctorId: current.doctorId || product.id,
                    paymentMethod: 'PIX',
                    scheduledAt: current.scheduledAt,
                    duration: current.duration,
                    isOnDuty: product.isOnDuty,
                    urgencyLevel: product.urgencyLevel,
                    ...(product.partner && { partner: product.partner }),
                  });
                }
                if (!result?.transactionId) throw new Error('ID da transação não retornado');
                if (!result.pixCode && result.paymentMethod === 'PIX') {
                  throw new Error('Dados do PIX não foram gerados corretamente');
                }
                await router.push(`/checkout/pix?transactionId=${result.transactionId}`);
              } catch (e: any) {
                toast({ title: 'Erro no processamento PIX', description: e?.message || 'Tente novamente.', variant: 'error' as any });
              } finally {
                setIsSubmitting(false);
              }
            }}
          />
        </div>
      </form>
    </FormProvider>
  );
}

function calculateTotal(basePrice: number, serviceFee?: number): number {
  let total = Number(basePrice);
  if (serviceFee) total += serviceFee;
  return total;
}


