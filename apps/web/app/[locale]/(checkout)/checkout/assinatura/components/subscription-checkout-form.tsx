'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useRouter } from 'next/navigation';
import { FormProvider, useForm } from 'react-hook-form';
import { useToast } from '@ui/hooks/use-toast';
import { useState } from 'react';
import { apiClient } from '@shared/lib/api-client';
import { z } from 'zod';

// Importar componentes reutilizáveis do checkout principal
import { CustomerForm } from '../../components/customer-form';
import { SubscriptionPaymentForm } from './subscription-payment-form';

// Schema específico para assinaturas
const subscriptionFormSchema = z.object({
  customerData: z.object({
    name: z.string().min(1, 'Nome é obrigatório'),
    email: z.string().email('Email inválido'),
    phone: z.string().min(10, 'Telefone inválido'),
    cpf: z.string().min(11, 'CPF inválido').max(14, 'CPF inválido'),
  }),
  paymentMethod: z.enum(['CREDIT_CARD', 'PIX_AUTO']),
  creditCard: z.object({
    cardNumber: z.string().optional(),
    cardHolder: z.string().optional(),
    cardExpiry: z.string().optional(),
    cardCvv: z.string().optional(),
    installments: z.number().default(1),
  }).optional(),
  // Campos específicos para assinaturas
  planId: z.string(),
  acceptTerms: z.boolean().refine(val => val === true, 'Você deve aceitar os termos'),
}).refine((data) => {
  // Validação condicional para cartão de crédito
  if (data.paymentMethod === 'CREDIT_CARD') {
    if (!data.creditCard) {
      return false;
    }

    const { cardNumber, cardHolder, cardExpiry, cardCvv } = data.creditCard;

    // Verificar se todos os campos obrigatórios estão preenchidos
    if (!cardNumber || cardNumber.length < 13) {
      return false;
    }
    if (!cardHolder || cardHolder.length < 2) {
      return false;
    }
    if (!cardExpiry || cardExpiry.length < 5) {
      return false;
    }
    if (!cardCvv || cardCvv.length < 3) {
      return false;
    }
  }

  return true;
}, {
  message: 'Dados do cartão de crédito são obrigatórios quando cartão de crédito for selecionado',
  path: ['creditCard'],
});

type SubscriptionFormData = z.infer<typeof subscriptionFormSchema>;

interface SubscriptionCheckoutFormProps {
  product: {
    id: string;
    title: string;
    description?: string;
    type: string;
    price: number;
    installmentsLimit: number;
    enableInstallments?: boolean;
    acceptedPayments?: string[];
    successUrl?: string;
    cancelUrl?: string;
    // Campos específicos para assinaturas
    originalPrice?: number;
    consultationsIncluded?: number;
    cycle?: string;
    features?: string[];
    isSubscription?: boolean;
  };
}

export function SubscriptionCheckoutForm({ product }: SubscriptionCheckoutFormProps) {
  const router = useRouter();
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Use tRPC mutation hook for subscriptions
  const createSubscriptionMutation = apiClient.subscriptions.createSubscription.useMutation();

  // Use default payment method from accepted payments, preferindo CREDIT_CARD primeiro
  const defaultPaymentMethod = product.acceptedPayments?.includes('CREDIT_CARD')
    ? 'CREDIT_CARD'
    : product.acceptedPayments?.includes('PIX_AUTO')
    ? 'PIX_AUTO'
    : 'CREDIT_CARD';

  const methods = useForm<SubscriptionFormData>({
    resolver: zodResolver(subscriptionFormSchema),
    defaultValues: {
      customerData: {
        name: '',
        email: '',
        phone: '',
        cpf: '',
      },
      planId: product.id,
      paymentMethod: defaultPaymentMethod as 'CREDIT_CARD' | 'PIX_AUTO',
      creditCard: {
        cardNumber: '',
        cardHolder: '',
        cardExpiry: '',
        cardCvv: '',
        installments: 1,
      },
      acceptTerms: false,
    },
  });

  const onSubmit = async (data: SubscriptionFormData) => {
    try {

      setIsSubmitting(true);

      // Validate required customer fields
      if (!data.customerData || typeof data.customerData !== 'object') {
        throw new Error('Dados do cliente não informados');
      }

      // Create safe customer data object
      const safeCustomerData = {
        name: data.customerData.name || '',
        email: data.customerData.email || '',
        phone: data.customerData.phone || '',
        cpf: data.customerData.cpf || '',
      };

      // Validate required customer fields
      if (!safeCustomerData.name || !safeCustomerData.email) {
        throw new Error('Nome e email são obrigatórios');
      }

      // Validate payment method specific requirements
      if (data.paymentMethod === 'CREDIT_CARD') {
        if (!data.creditCard?.cardNumber || !data.creditCard?.cardHolder ||
            !data.creditCard?.cardExpiry || !data.creditCard?.cardCvv) {
          throw new Error('Dados do cartão de crédito são obrigatórios');
        }
      }

      // Display loading state
      toast({
        title: 'Processando assinatura',
        description: 'Aguarde enquanto processamos sua assinatura...',
        variant: 'default',
      });

      // Preparar dados para a API de assinatura
      const billingType = data.paymentMethod === 'PIX_AUTO' ? 'PIX' as const : data.paymentMethod;

      const subscriptionData = {
        planId: data.planId,
        billingType: billingType,
        customerData: safeCustomerData,
        ...(data.paymentMethod === 'CREDIT_CARD' && data.creditCard && {
          creditCard: {
            holderName: data.creditCard.cardHolder || '',
            number: data.creditCard.cardNumber?.replace(/\D/g, '') || '',
            expiryMonth: data.creditCard.cardExpiry?.split('/')[0] || '',
            expiryYear: `20${data.creditCard.cardExpiry?.split('/')[1] || ''}`,
            ccv: data.creditCard.cardCvv || '',
          },
          creditCardHolderInfo: {
            name: safeCustomerData.name,
            email: safeCustomerData.email,
            cpfCnpj: safeCustomerData.cpf.replace(/\D/g, ''),
            postalCode: '01311000', // Código postal padrão
            addressNumber: '123',
            phone: safeCustomerData.phone.replace(/\D/g, ''),
            mobilePhone: safeCustomerData.phone.replace(/\D/g, ''),
          }
        }),
        remoteIp: window.location.hostname // Adicionar IP para segurança
      };



      const result = await createSubscriptionMutation.mutateAsync(subscriptionData);



      toast({
        title: 'Assinatura criada com sucesso!',
        description: result.message || 'Sua assinatura foi ativada e você já pode começar a usar os benefícios.',
        variant: 'default',
      });

      // Handle different scenarios based on payment method and result
      // Sem paymentLink: redirecionar para página de sucesso da assinatura
      const queryParams = new URLSearchParams({
        subscriptionId: result.subscription?.id || '',
        planName: result.plan?.name || '',
        amount: result.plan?.price?.toString() || '',
      });

      // Adicionar parâmetros adicionais se disponíveis
      if (result.magicUrl) {
        queryParams.append('magicUrl', result.magicUrl);
      }
      if (result.paymentStatus) {
        queryParams.append('paymentStatus', result.paymentStatus);
      }
      if (result.paymentCharged !== undefined) {
        queryParams.append('paymentCharged', String(result.paymentCharged));
      }

      router.push(`/checkout/success/subscription?${queryParams.toString()}`);

    } catch (error: any) {
      console.error('Erro ao criar assinatura:', error);

      // Parse error message for better user experience
      let errorMessage = 'Tente novamente em alguns minutos.';

      if (error.message?.includes('assinatura ativa')) {
        errorMessage = 'Você já possui uma assinatura ativa.';
      } else if (error.message?.includes('cartão')) {
        errorMessage = 'Verifique os dados do seu cartão e tente novamente.';
      } else if (error.message?.includes('cliente')) {
        errorMessage = 'Verifique seus dados pessoais e tente novamente.';
      } else if (error.message) {
        errorMessage = error.message;
      }

      toast({
        title: 'Erro ao processar assinatura',
        description: errorMessage,
        variant: 'error',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const calculateTotal = (basePrice: number): number => {
    return Number(basePrice);
  };

  return (
    <FormProvider {...methods}>
      <form
        onSubmit={methods.handleSubmit(onSubmit)}
        className='container max-w-6xl mx-auto px-0 lg:px-4'
      >
        <div className='lg:col-span-8 space-y-6'>
          <CustomerForm />

          <SubscriptionPaymentForm
            totalAmount={calculateTotal(product.price)}
            installmentsLimit={product.installmentsLimit}
            enableInstallments={product.enableInstallments}
            acceptedPayments={product.acceptedPayments}
            loading={isSubmitting}
            onPixAutoClick={async () => {
              await methods.handleSubmit(onSubmit)();
            }}
          />
        </div>
      </form>
    </FormProvider>
  );
}
