'use client';

import { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader } from '@ui/components/card';
import { CheckCircle2, ArrowLeft, CreditCard } from 'lucide-react';
import { Button } from '@ui/components/button';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import { CheckoutSteps } from '../../components/checkout-steps';

export default function SubscriptionSuccessPage() {
  const searchParams = useSearchParams();
  const [subscriptionId, setSubscriptionId] = useState<string>('');
  const [planName, setPlanName] = useState<string>('');
  const [amount, setAmount] = useState<number>(0);
  const [magicUrl, setMagicUrl] = useState<string>('');
  const [autoRedirecting, setAutoRedirecting] = useState<boolean>(false);
  const [paymentStatus, setPaymentStatus] = useState<string>('');
  const [paymentCharged, setPaymentCharged] = useState<boolean>(false);

  useEffect(() => {
    setSubscriptionId(searchParams.get('subscriptionId') || '');
    setPlanName(searchParams.get('planName') || 'Assinatura');
    const amountStr = searchParams.get('amount') || '0';
    setAmount(parseFloat(amountStr) || 0);
    const magicUrlParam = searchParams.get('magicUrl') || '';
    setMagicUrl(magicUrlParam);
    setPaymentStatus(searchParams.get('paymentStatus') || '');
    setPaymentCharged(searchParams.get('paymentCharged') === 'true');

    // Se tiver magicUrl, redirecionar automaticamente após 3 segundos
    if (magicUrlParam) {
      setAutoRedirecting(true);
      const timeout = setTimeout(() => {
        window.location.href = magicUrlParam;
      }, 3000);

      return () => clearTimeout(timeout);
    }
  }, [searchParams]);

  return (
    <div className='min-h-screen bg-gradient-to-b from-white to-gray-50/50'>
      <div className='container max-w-2xl py-16'>
        <div className='max-w-xl mx-auto mb-8'>
          <CheckoutSteps currentStep='payment' />
        </div>

        <Card className='mb-8'>
          <CardHeader className='text-center pb-6'>
            <div className='mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-primary/10'>
              <CheckCircle2 className='h-6 w-6 text-primary' />
            </div>
            <h1 className='text-2xl font-semibold'>Assinatura Ativada!</h1>
            <p className='text-muted-foreground'>Seu pagamento foi confirmado e sua assinatura está ativa</p>
          </CardHeader>

          <CardContent className='border-t pt-6'>
            <div className='space-y-4'>
              {subscriptionId && (
                <div className='flex justify-between text-sm'>
                  <span className='text-muted-foreground'>ID da Assinatura</span>
                  <span className='font-medium'>{subscriptionId}</span>
                </div>
              )}

              {paymentStatus && (
                <div className='flex justify-between text-sm'>
                  <span className='text-muted-foreground'>Status do Pagamento</span>
                  <span className={`font-medium ${
                    paymentCharged ? 'text-green-600' : 'text-yellow-600'
                  }`}>
                    {paymentCharged ? '✅ Pago' : '⏳ Processando'}
                  </span>
                </div>
              )}

              <div className='rounded-lg bg-muted p-4 text-sm text-center text-muted-foreground'>
                Você receberá um email e uma mensagem no WhatsApp com os detalhes do seu plano
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className='mb-6'>
          <CardHeader className='pb-3'>
            <div className='flex items-center gap-2'>
              <CreditCard className='h-5 w-5' />
              <h2 className='text-xl font-semibold'>Informações da assinatura</h2>
            </div>
          </CardHeader>

          <CardContent>
            <div className='space-y-4'>
              <p className='text-muted-foreground'>
                Seu plano <span className='font-medium'>{planName || 'ZapVida'}</span> foi ativado. Você já pode utilizar os benefícios disponíveis no aplicativo.
              </p>

              {autoRedirecting && magicUrl ? (
                <div className='bg-blue-50 border border-blue-100 p-4 rounded-lg text-blue-700 text-sm'>
                  <p className='font-medium mb-1'>🎉 Login Automático Ativado!</p>
                  <p>Você será redirecionado automaticamente para sua conta em alguns segundos...</p>
                  <div className='mt-3'>
                    <Button asChild className='w-full'>
                      <a href={magicUrl}>Acessar agora</a>
                    </Button>
                  </div>
                </div>
              ) : (
                <>
                  <div className='bg-green-50 border border-green-100 p-4 rounded-lg text-green-700 text-sm'>
                    <p className='font-medium mb-1'>Como aproveitar sua assinatura:</p>
                    <ol className='list-decimal pl-5 space-y-1'>
                      <li>Acesse sua conta ZapVida</li>
                      <li>Agende consultas incluídas no seu plano</li>
                      <li>Acompanhe seu uso em "Minha Assinatura"</li>
                    </ol>
                  </div>

                  <Button asChild className='w-full'>
                    {magicUrl ? (
                      <a href={magicUrl}>Acessar minha assinatura</a>
                    ) : (
                      <Link href='/auth/login'>Acessar minha assinatura</Link>
                    )}
                  </Button>
                </>
              )}

              <div className='bg-muted p-4 rounded-lg'>
                <p className='text-sm text-muted-foreground'>
                  Verifique seu email e WhatsApp para mais informações. Caso não receba, confira a caixa de spam.
                </p>
              </div>

              <hr />

              <div className='flex justify-center text-sm'>
                <Link href='/' className='flex items-center gap-2'>
                  <ArrowLeft className='h-4 w-4' />
                  Voltar para a página inicial
                </Link>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}


