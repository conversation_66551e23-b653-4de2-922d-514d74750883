"use client";

import { useState, useRef, useEffect, useCallback } from "react";
import { But<PERSON> } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Avatar, AvatarFallback, AvatarImage } from "@ui/components/avatar";
import { Card, CardContent, CardHeader } from "@ui/components/card";
import { Badge } from "@ui/components/badge";
import {
  Send,
  Mic,
  Paperclip,
  Phone,
  Video,
  MoreVertical,
  FileText,
  FileAudio,
  FileImage,
  Download,
  Play,
  Pause,
  Volume2,
  X,
  AlertCircle,
  CheckCircle,
  Clock,
  Wifi,
  WifiOff,
  ArrowLeft,
  Settings,
  Smile,
  Plus,
  RefreshCw,
} from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { ptBR } from "date-fns/locale";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@ui/components/dropdown-menu";
import { toast } from "sonner";

// Tipos
import { ChatMessage, ConnectionStatus, TypingStatus } from "../../lib/services/supabase-realtime.service";
import { cn } from "@ui/lib";

interface PremiumChatInterfaceProps {
  // Dados da consulta
  consultation: {
    id: string;
    doctor_name?: string;
    doctor_avatar?: string;
    patient_name?: string;
    patient_avatar?: string;
    is_on_duty?: boolean;
  };

  // Estado do usuário
  userId: string;
  userRole: 'DOCTOR' | 'PATIENT';

  // Dados do chat
  messages: ChatMessage[];
  isLoading: boolean;
  error: string | null;

  // Estado da conexão
  connectionStatus: ConnectionStatus;
  isConnected: boolean;

  // Estado de digitação
  isTyping: boolean;
  typingUsers: TypingStatus[];

  // Funções do chat
  onSendMessage: (content: string) => Promise<void>;
  onSendAudio: (audioBlob: Blob) => Promise<void>;
  onSendFile: (file: File) => Promise<void>;
  onStartTyping: () => void;
  onStopTyping: () => void;

  // Controles de conexão
  onRefresh: () => Promise<void>;
  onReconnect: () => Promise<void>;
  onClearError: () => void;

  // Navegação
  onBack?: () => void;
  onEndConsultation?: () => void;

  // Integrações opcionais
  onVideoCall?: () => void;
  onAudioCall?: () => void;
  onOpenMedicalRecord?: () => void;
}

export function PremiumChatInterface({
  consultation,
  userId,
  userRole,
  messages,
  isLoading,
  error,
  connectionStatus,
  isConnected,
  isTyping,
  typingUsers,
  onSendMessage,
  onSendAudio,
  onSendFile,
  onStartTyping,
  onStopTyping,
  onRefresh,
  onReconnect,
  onClearError,
  onBack,
  onEndConsultation,
  onVideoCall,
  onAudioCall,
  onOpenMedicalRecord,
}: PremiumChatInterfaceProps) {

  // Estados locais
  const [inputValue, setInputValue] = useState("");
  const [isRecording, setIsRecording] = useState(false);
  const [mediaRecorder, setMediaRecorder] = useState<MediaRecorder | null>(null);
  const [audioChunks, setAudioChunks] = useState<Blob[]>([]);
  const [playingAudioId, setPlayingAudioId] = useState<string | null>(null);
  const [audioElements, setAudioElements] = useState<Map<string, HTMLAudioElement>>(new Map());
  const [showConnectionDetails, setShowConnectionDetails] = useState(false);

  // Refs
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Dados do destinatário baseado no role
  const recipientName = userRole === 'DOCTOR' ? consultation.patient_name : consultation.doctor_name;
  const recipientAvatar = userRole === 'DOCTOR' ? consultation.patient_avatar : consultation.doctor_avatar;
  const recipientInitial = recipientName?.charAt(0) || (userRole === 'DOCTOR' ? 'P' : 'D');

  // Auto-scroll para nova mensagem
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  // Auto-focus no input
  useEffect(() => {
    inputRef.current?.focus();
  }, []);

  // Configurar MediaRecorder
  useEffect(() => {
    if (typeof window !== "undefined" && navigator.mediaDevices) {
      navigator.mediaDevices
        .getUserMedia({ audio: true })
        .then((stream) => {
          const recorder = new MediaRecorder(stream);
          setMediaRecorder(recorder);

          recorder.ondataavailable = (event) => {
            if (event.data.size > 0) {
              setAudioChunks((prev) => [...prev, event.data]);
            }
          };

          recorder.onstop = async () => {
            const audioBlob = new Blob(audioChunks, { type: "audio/wav" });
            setAudioChunks([]);

            try {
              await onSendAudio(audioBlob);
              toast.success("Áudio enviado com sucesso");
            } catch (error) {
              console.error("Erro ao enviar áudio:", error);
              toast.error("Erro ao enviar áudio");
            }
          };
        })
        .catch((error) => {
          console.error("Erro ao acessar microfone:", error);
          toast.error("Erro ao acessar microfone");
        });
    }
  }, [onSendAudio, audioChunks]);

  // Funções de envio
  const handleSendMessage = async () => {
    if (!inputValue.trim()) return;

    const content = inputValue.trim();
    setInputValue("");
    onStopTyping();

    try {
      await onSendMessage(content);
    } catch (error) {
      console.error("Erro ao enviar mensagem:", error);
      // Restaurar mensagem em caso de erro
      setInputValue(content);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setInputValue(value);

    // Gerenciar indicador de digitação
    if (value.trim() && !isTyping) {
      onStartTyping();
    }

    // Limpar timeout anterior
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Parar digitação após 1 segundo de inatividade
    typingTimeoutRef.current = setTimeout(() => {
      onStopTyping();
    }, 1000);
  };

  // Funções de áudio
  const startRecording = () => {
    if (mediaRecorder && mediaRecorder.state === "inactive") {
      setAudioChunks([]);
      mediaRecorder.start();
      setIsRecording(true);
      toast.info("Gravando áudio...");
    }
  };

  const stopRecording = () => {
    if (mediaRecorder && mediaRecorder.state === "recording") {
      mediaRecorder.stop();
      setIsRecording(false);
    }
  };

  // Funções de arquivo
  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      try {
        await onSendFile(file);
        toast.success("Arquivo enviado com sucesso");
      } catch (error) {
        console.error("Erro ao enviar arquivo:", error);
        toast.error("Erro ao enviar arquivo");
      }
    }
    // Limpar input
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const handleFileClick = (fileUrl: string, fileName: string) => {
    const link = document.createElement("a");
    link.href = fileUrl;
    link.download = fileName;
    link.target = "_blank";
    link.click();
  };

  // Funções de reprodução de áudio
  const toggleAudioPlayback = (audioUrl: string, messageId: string) => {
    const existingAudio = audioElements.get(messageId);

    if (existingAudio) {
      if (playingAudioId === messageId) {
        existingAudio.pause();
        setPlayingAudioId(null);
      } else {
        // Parar áudio anterior se estiver tocando
        if (playingAudioId) {
          const previousAudio = audioElements.get(playingAudioId);
          if (previousAudio) {
            previousAudio.pause();
            previousAudio.currentTime = 0;
          }
        }

        existingAudio.play();
        setPlayingAudioId(messageId);
      }
    } else {
      const audio = new Audio(audioUrl);
      audio.onended = () => setPlayingAudioId(null);
      audio.onerror = () => {
        setPlayingAudioId(null);
        toast.error("Erro ao reproduzir áudio");
      };

      setAudioElements(prev => new Map(prev).set(messageId, audio));
      audio.play();
      setPlayingAudioId(messageId);
    }
  };

  // Utilitários
  const getFileIcon = (fileName: string) => {
    const extension = fileName.split('.').pop()?.toLowerCase();

    if (['mp3', 'wav', 'ogg', 'm4a'].includes(extension || '')) {
      return <FileAudio className="w-4 h-4" />;
    } else if (['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(extension || '')) {
      return <FileImage className="w-4 h-4" />;
    } else {
      return <FileText className="w-4 h-4" />;
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Garante que timestamps no futuro (diferenças de timezone) não exibam "em ..."
  const relativeFromNowSafe = (dateInput: string | number | Date) => {
    const original = new Date(dateInput);
    const safe = new Date(Math.min(original.getTime(), Date.now()));
    return formatDistanceToNow(safe, { addSuffix: true, locale: ptBR });
  };

  const getConnectionStatusIcon = () => {
    switch (connectionStatus.status) {
      case 'connected':
        return <Wifi className="w-4 h-4 text-green-500" />;
      case 'connecting':
        return <Clock className="w-4 h-4 text-yellow-500 animate-pulse" />;
      case 'reconnecting':
        return <RefreshCw className="w-4 h-4 text-yellow-500 animate-spin" />;
      case 'error':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      default:
        return <WifiOff className="w-4 h-4 text-gray-400" />;
    }
  };

  const getConnectionStatusText = () => {
    const statusText = {
      'connected': 'Conectado',
      'connecting': 'Conectando...',
      'disconnected': 'Desconectado',
      'error': 'Erro de conexão',
      'reconnecting': 'Reconectando...'
    }[connectionStatus.status] || 'Status desconhecido';

    if (connectionStatus.latency) {
      return `${statusText} (${connectionStatus.latency}ms)`;
    }

    return statusText;
  };

  const getConnectionStatusColor = () => {
    switch (connectionStatus.status) {
      case 'connected':
        return 'text-green-600';
      case 'connecting':
      case 'reconnecting':
        return 'text-yellow-600';
      case 'error':
        return 'text-red-600';
      default:
        return 'text-gray-500';
    }
  };

  return (
    <div className="flex flex-col h-full bg-gradient-to-br from-blue-50 to-indigo-50">
      {/* Header Premium - Melhorado */}
      <CardHeader className="bg-white/90 backdrop-blur-sm border-b border-gray-200 shadow-sm">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            {onBack && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onBack}
                className="shrink-0 hover:bg-gray-100"
              >
                <ArrowLeft className="w-4 h-4" />
              </Button>
            )}

            <Avatar className="w-10 h-10 ring-2 ring-white shadow-sm">
              <AvatarImage src={recipientAvatar} alt={recipientName} />
              <AvatarFallback className="bg-gradient-to-br from-purple-500 to-blue-600 text-white font-medium">
                {recipientInitial}
              </AvatarFallback>
            </Avatar>

            <div className="min-w-0 flex-1">
              <h2 className="font-semibold text-gray-900 truncate text-lg">
                {recipientName || (userRole === 'DOCTOR' ? 'Paciente' : 'Médico')}
              </h2>
              <div className="flex items-center gap-2">
                <Badge
                  variant={consultation.is_on_duty ? "destructive" : "secondary"}
                  className="text-xs font-medium"
                >
                  {consultation.is_on_duty ? "PLANTÃO" : "CONSULTA"}
                </Badge>

                {/* Status da conexão melhorado */}
                <div className="flex items-center gap-1 text-xs">
                  {getConnectionStatusIcon()}
                  <span className={`font-medium ${getConnectionStatusColor()}`}>
                    {isConnected ? 'Online' : 'Offline'}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Ações do header */}
          <div className="flex items-center gap-1">
            {onAudioCall && (
              <Button variant="ghost" size="sm" onClick={onAudioCall}>
                <Phone className="w-4 h-4" />
              </Button>
            )}

            {onVideoCall && (
              <Button variant="ghost" size="sm" onClick={onVideoCall}>
                <Video className="w-4 h-4" />
              </Button>
            )}

            {/* Doutor: botão Finalizar visível e destacado */}
            {userRole === 'DOCTOR' && onEndConsultation && (
              <Button
                onClick={onEndConsultation}
                size="sm"
                className="bg-red-600 hover:bg-red-700 text-white"
              >
                Finalizar
              </Button>
            )}

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm">
                  <MoreVertical className="w-4 h-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                {onOpenMedicalRecord && userRole === 'DOCTOR' && (
                  <DropdownMenuItem onClick={onOpenMedicalRecord}>
                    <FileText className="w-4 h-4 mr-2" />
                    Prontuário
                  </DropdownMenuItem>
                )}
                <DropdownMenuItem onClick={onRefresh}>
                  <RefreshCw className="w-4 h-4 mr-2" />
                  Atualizar
                </DropdownMenuItem>
                <DropdownMenuItem onClick={onReconnect}>
                  <Settings className="w-4 h-4 mr-2" />
                  Reconectar
                </DropdownMenuItem>
                {onEndConsultation && (
                  <DropdownMenuItem onClick={onEndConsultation} className="text-red-600">
                    <X className="w-4 h-4 mr-2" />
                    Finalizar Consulta
                  </DropdownMenuItem>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {/* Detalhes da conexão expandidos */}
        {showConnectionDetails && (
          <div className="mt-4 p-3 bg-white/60 rounded-lg border border-white/30 text-sm">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <span className="font-medium text-gray-700">Status:</span>
                <div className="flex items-center gap-2 mt-1">
                  {getConnectionStatusIcon()}
                  <span className={getConnectionStatusColor()}>
                    {getConnectionStatusText()}
                  </span>
                </div>
              </div>
              <div>
                <span className="font-medium text-gray-700">Tentativas:</span>
                <span className="block mt-1 text-gray-600">
                  {connectionStatus.reconnectAttempts}
                </span>
              </div>
              {connectionStatus.lastConnected && (
                <div className="col-span-2">
                  <span className="font-medium text-gray-700">Última conexão:</span>
                  <span className="block mt-1 text-gray-600">
                    {formatDistanceToNow(connectionStatus.lastConnected, {
                      addSuffix: true,
                      locale: ptBR
                    })}
                  </span>
                </div>
              )}
              {connectionStatus.lastError && (
                <div className="col-span-2">
                  <span className="font-medium text-red-600">Erro:</span>
                  <span className="block mt-1 text-red-600 text-xs">
                    {connectionStatus.lastError}
                  </span>
                </div>
              )}
            </div>
          </div>
        )}
      </CardHeader>

      {/* Área de mensagens - Melhorada */}
      <CardContent className="flex-1 overflow-y-auto p-6 space-y-4 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-transparent">
        {isLoading && messages.length === 0 ? (
          <div className="space-y-4">
            {[1,2,3,4,5].map((i) => (
              <div key={i} className={cn("flex gap-3", i % 2 === 0 ? "flex-row-reverse" : "flex-row")}>
                <div className="w-8 h-8 rounded-full bg-gray-200 shrink-0" />
                <div className={cn("max-w-[70%] flex flex-col", i % 2 === 0 ? "items-end" : "items-start")}>
                  <div className={cn(
                    "rounded-2xl px-4 py-3 shadow-sm",
                    i % 2 === 0 ? "bg-gradient-to-br from-blue-100 to-blue-200" : "bg-white/80"
                  )}>
                    <div className="space-y-2">
                      <div className="h-4 bg-gray-300 rounded w-48" />
                      <div className="h-4 bg-gray-300 rounded w-32" />
                      {i % 3 === 0 && <div className="h-4 bg-gray-300 rounded w-24" />}
                    </div>
                  </div>
                  <div className="flex items-center gap-2 mt-2 px-1">
                    <div className="h-3 w-16 bg-gray-200 rounded" />
                    {i % 2 === 0 && <div className="w-3 h-3 bg-gray-200 rounded-full" />}
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : messages.length === 0 ? (
          <div className="text-center py-12">
            <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-blue-100 to-purple-100 rounded-full flex items-center justify-center">
              <Send className="w-8 h-8 text-blue-500" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Inicie a conversa
            </h3>
            <p className="text-gray-500">
              Envie uma mensagem para começar o atendimento
            </p>
          </div>
        ) : (
          messages.map((message) => {
            const isOwnMessage = message.senderId === userId;
            const isAudio = message.type === 'AUDIO';
            const isFile = message.type === 'FILE';
            const isSystem = message.type === 'SYSTEM';

            if (isSystem) {
              return (
                <div key={message.id} className="flex justify-center">
                  <Badge variant="secondary" className="text-xs bg-white/70 text-gray-600">
                    {message.content}
                  </Badge>
                </div>
              );
            }

            return (
              <div
                key={message.id}
                className={cn(
                  "flex gap-3 transition-all duration-200",
                  isOwnMessage ? "flex-row-reverse" : ""
                )}
              >
                <Avatar className="w-8 h-8 ring-2 ring-white shadow-sm">
                  <AvatarImage
                    src={isOwnMessage ? undefined : recipientAvatar}
                    alt={isOwnMessage ? "Você" : recipientName}
                  />
                  <AvatarFallback className="bg-gradient-to-br from-gray-100 to-gray-200 text-gray-600 text-xs">
                    {isOwnMessage ? "V" : recipientInitial}
                  </AvatarFallback>
                </Avatar>

                <div className={cn(
                  "flex flex-col max-w-[70%] transition-all duration-200",
                  isOwnMessage ? "items-end" : "items-start"
                )}>
                  <div
                    className={cn(
                      "rounded-2xl px-4 py-3 shadow-sm backdrop-blur-sm transition-all duration-200 hover:shadow-md",
                      isOwnMessage
                        ? "bg-gradient-to-br from-blue-500 to-blue-600 text-white rounded-br-md"
                        : "bg-white/80 text-gray-900 rounded-bl-md border border-white/50"
                    )}
                  >
                    {isAudio ? (
                      <div className="flex items-center gap-3 min-w-[200px]">
                        <Button
                          variant="ghost"
                          size="sm"
                          className={cn(
                            "p-2 h-auto rounded-full",
                            isOwnMessage
                              ? "text-white hover:bg-white/20"
                              : "text-blue-600 hover:bg-blue-50"
                          )}
                          onClick={() => toggleAudioPlayback(message.file_url || '', message.id)}
                        >
                          {playingAudioId === message.id ? (
                            <Pause className="w-5 h-5" />
                          ) : (
                            <Play className="w-5 h-5" />
                          )}
                        </Button>
                        <div className="flex-1">
                          <div className="flex items-center gap-2">
                            <Volume2 className="w-4 h-4 opacity-70" />
                            <span className="text-sm font-medium">Mensagem de áudio</span>
                          </div>
                          {message.file_size && (
                            <span className="text-xs opacity-75 mt-1 block">
                              {formatFileSize(message.file_size)}
                            </span>
                          )}
                        </div>
                      </div>
                    ) : isFile ? (
                      <div className="flex items-center gap-3 min-w-[200px]">
                        <div className={cn(
                          "p-2 rounded-lg",
                          isOwnMessage ? "bg-white/20" : "bg-blue-50"
                        )}>
                          {getFileIcon(message.file_name || '')}
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium truncate">{message.content}</p>
                          {message.file_size && (
                            <span className="text-xs opacity-75">
                              {formatFileSize(message.file_size)}
                            </span>
                          )}
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          className={cn(
                            "p-2 h-auto rounded-full shrink-0",
                            isOwnMessage
                              ? "text-white hover:bg-white/20"
                              : "text-blue-600 hover:bg-blue-50"
                          )}
                          onClick={() => handleFileClick(message.file_url || '', message.file_name || '')}
                        >
                          <Download className="w-4 h-4" />
                        </Button>
                      </div>
                    ) : (
                      <p className="text-sm leading-relaxed whitespace-pre-wrap">
                        {message.content}
                      </p>
                    )}
                  </div>
                  <div className="flex items-center gap-2 mt-2 px-1">
                    <span className="text-xs text-gray-500">
                      {relativeFromNowSafe(message.createdAt)}
                    </span>
                    {isOwnMessage && (
                      <CheckCircle className="w-3 h-3 text-blue-500" />
                    )}
                  </div>
                </div>
              </div>
            );
          })
        )}

        {/* Indicadores de digitação */}
        {typingUsers.length > 0 && (
          <div className="flex gap-3">
            <Avatar className="w-8 h-8 ring-2 ring-white shadow-sm">
              <AvatarImage src={recipientAvatar} alt={recipientName} />
              <AvatarFallback className="bg-gradient-to-br from-gray-100 to-gray-200 text-gray-600 text-xs">
                {recipientInitial}
              </AvatarFallback>
            </Avatar>
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl rounded-bl-md px-4 py-3 shadow-sm border border-white/50">
              <div className="flex items-center gap-2">
                <div className="flex gap-1">
                  <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" />
                  <div
                    className="w-2 h-2 bg-blue-500 rounded-full animate-bounce"
                    style={{ animationDelay: '0.1s' }}
                  />
                  <div
                    className="w-2 h-2 bg-blue-500 rounded-full animate-bounce"
                    style={{ animationDelay: '0.2s' }}
                  />
                </div>
                <span className="text-sm text-gray-600 ml-2">
                  {typingUsers.length === 1
                    ? `${typingUsers[0].userName} está digitando...`
                    : `${typingUsers.length} pessoas estão digitando...`
                  }
                </span>
              </div>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </CardContent>

      {/* Input Premium - Melhorado */}
      <div className="sticky bottom-0 w-full border-t border-gray-200 bg-white/95 backdrop-blur-sm shadow-lg">
        <div className="max-w-4xl mx-auto p-4">
        {/* Indicador de erro */}
        {error && (
          <div className="mb-3 p-3 bg-red-50/80 backdrop-blur-sm border border-red-200/50 rounded-lg flex items-center gap-2">
            <AlertCircle className="w-4 h-4 text-red-500 shrink-0" />
            <span className="text-sm text-red-600 flex-1">{error}</span>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClearError}
              className="p-1 h-auto text-red-600 hover:bg-red-100/50"
            >
              <X className="w-4 h-4" />
            </Button>
          </div>
        )}

        <div className="flex items-center gap-3">
          {/* Botão de anexo */}
          <Button
            variant="outline"
            size="sm"
            onClick={() => fileInputRef.current?.click()}
            disabled={!isConnected}
            className="w-10 h-10 p-0 rounded-full bg-white border-gray-300 hover:bg-gray-50 shadow-sm"
          >
            <Paperclip className="w-4 h-4" />
          </Button>

          {/* Input principal melhorado */}
          <div className="flex-1 relative">
            <Input
              ref={inputRef}
              value={inputValue}
              onChange={handleInputChange}
              onKeyPress={handleKeyPress}
              placeholder="Digite sua mensagem..."
              className="bg-white border-gray-300 focus:border-blue-400 focus:ring-blue-200 rounded-full h-12 pl-4 pr-12 shadow-sm"
              disabled={!isConnected}
            />
            <Button
              variant="ghost"
              size="sm"
              className="absolute right-2 top-1/2 -translate-y-1/2 p-1 h-8 w-8 rounded-full text-gray-500 hover:text-gray-700"
              disabled={!isConnected}
            >
              <Smile className="w-4 h-4" />
            </Button>
          </div>

          {/* Botão de áudio */}
          <Button
            variant="outline"
            size="sm"
            onMouseDown={startRecording}
            onMouseUp={stopRecording}
            onMouseLeave={stopRecording}
            disabled={!isConnected || !mediaRecorder}
            className={cn(
              "w-10 h-10 p-0 rounded-full bg-white border-gray-300 hover:bg-gray-50 transition-all duration-200 shadow-sm",
              isRecording && "bg-red-500 text-white border-red-500 hover:bg-red-600 animate-pulse"
            )}
          >
            <Mic className="w-4 h-4" />
          </Button>

          {/* Enviar */}
          <Button
            onClick={handleSendMessage}
            disabled={!inputValue.trim() || !isConnected}
            className="w-10 h-10 p-0 rounded-full bg-blue-500 hover:bg-blue-600 text-white shadow-sm"
          >
            <Send className="w-4 h-4" />
          </Button>
        </div>

        {/* Input de arquivo oculto */}
        <input
          ref={fileInputRef}
          type="file"
          className="hidden"
          onChange={handleFileSelect}
          accept="image/*,audio/*,.pdf,.doc,.docx,.txt"
        />
        </div>

        {/* Estado de conexão no input */}
        {!isConnected && (
          <div className="flex items-center justify-center gap-2 mt-2 text-xs text-gray-500">
            <WifiOff className="w-3 h-3" />
            <span>Reconectando...</span>
          </div>
        )}
      </div>
    </div>
  );
}
