"use client";

import { useState, useEffect, useCallback, useMemo } from "react";
import type { User } from 'database';
import { ConsultationService, Consultation } from "../../lib/services/consultation.service";
import { PremiumChatInterface } from "./premium-chat-interface";
import { ConsultationList } from "./consultation-list";
import { NotificationProvider, useChatNotifications } from "./chat-notification";
import { useOptimizedChat } from "../../lib/hooks/use-optimized-chat";
import { VideoAudioModal } from "./video-audio-modal";
import { MobileChatInterface } from "./mobile-chat-interface";
import { RefreshCw, MessageCircle, AlertCircle, ArrowLeft, Plus, Video, Phone, X, VideoOff } from "lucide-react";
import { Button } from "@ui/components/button";
import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { Badge } from "@ui/components/badge";
import { useRouter } from "next/navigation";
import { cn } from "@ui/lib";
import { SupabaseRealtimeDebugPanel } from "./supabase-realtime-debug-panel";

interface NewZapChatClientProps {
  user: User;
  enableVideo?: boolean;
}

function ZapChatContent({ user, enableVideo = true }: NewZapChatClientProps) {
  const router = useRouter();
  const [consultations, setConsultations] = useState<Consultation[]>([]);
  const [selectedConsultation, setSelectedConsultation] = useState<Consultation | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [showCallModal, setShowCallModal] = useState(false);
  const [callMode, setCallMode] = useState<"video" | "audio">("video");
  const [isMobile, setIsMobile] = useState(false);
  const [subscriptionSummary, setSubscriptionSummary] = useState<{
    hasSubscription: boolean;
    consultationsIncluded: number;
    consultationsUsed: number;
    remainingConsultations: number;
    planName?: string;
  } | null>(null);
  const [isLoadingSubscription, setIsLoadingSubscription] = useState(false);

  const { addNotification } = useChatNotifications();
  const consultationService = useMemo(() => new ConsultationService(), []);
  const userRole = user.role === "DOCTOR" ? "DOCTOR" : "PATIENT";

  // Usar o novo hook otimizado apenas quando há consulta selecionada
  const chatHook = useOptimizedChat({
    appointmentId: selectedConsultation?.id || '',
    userId: user.id,
    userName: user.name || 'Usuário',
    userRole: userRole,
    autoConnect: !!selectedConsultation,
    enableTypingIndicators: true,
  });

  const loadConsultations = useCallback(async (showRefreshIndicator = false) => {
    if (!user?.id) {
      console.log("[NewZapChat] Sem user.id para carregar consultas");
      return;
    }

    console.log("[NewZapChat] Carregando consultas para usuário:", user.id, "role:", userRole);

    if (showRefreshIndicator) {
      setIsRefreshing(true);
    } else {
      setIsLoading(true);
    }

    setError(null);

    try {
      const data = await consultationService.getActiveConsultations(user.id, userRole.toLowerCase() as 'doctor' | 'patient');
      console.log("[NewZapChat] Consultas recebidas:", data.length, data);
      setConsultations(data);

      // Auto-selecionar primeira consulta se não houver nenhuma selecionada
      if (data.length > 0 && !selectedConsultation) {
        setSelectedConsultation(data[0]);
      }

      if (showRefreshIndicator && data.length > 0) {
        addNotification({
          type: "success",
          title: "Consultas atualizadas",
          message: `${data.length} consulta(s) carregada(s) com sucesso`,
          duration: 3000
        });
      }
    } catch (err) {
      console.error("[NewZapChat] Erro ao carregar consultas:", err);
      const errorMessage = err instanceof Error ? err.message : "Failed to load consultations";
      setError(errorMessage);

      addNotification({
        type: "error",
        title: "Erro ao carregar consultas",
        message: errorMessage,
        duration: 5000
      });
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  }, [user?.id, userRole, selectedConsultation, addNotification, consultationService]);

  useEffect(() => {
    if (user?.id) {
      loadConsultations();
    }
  }, [user?.id, loadConsultations]);

  // Detectar se é mobile
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 1024); // lg breakpoint
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Carregar créditos de assinatura (somente paciente)
  useEffect(() => {
    const loadSubscriptionSummary = async () => {
      if (userRole !== "PATIENT") return;
      try {
        setIsLoadingSubscription(true);
        const res = await fetch('/api/appointments/subscription');
        if (res.ok) {
          const data = await res.json();
          setSubscriptionSummary(data);
        }
      } catch (e) {
        console.error('[NewZapChat] Erro ao buscar créditos da assinatura:', e);
      } finally {
        setIsLoadingSubscription(false);
      }
    };
    loadSubscriptionSummary();
  }, [userRole]);

  const handleConsultationSelect = (consultation: Consultation) => {
    setSelectedConsultation(consultation);
    // Em mobile, não fazer nada especial - o layout responsivo cuidará
  };

  const handleStartConsultation = async (consultationId: string) => {
    try {
      setIsLoading(true);
      await consultationService.startConsultation(consultationId);
      // Só recarregar consultas se necessário
      if (consultations.length === 0) {
        await loadConsultations();
      }

      addNotification({
        type: "success",
        title: "Consulta iniciada",
        message: "A consulta foi iniciada com sucesso",
        duration: 3000
      });
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to start consultation";
      setError(errorMessage);

      addNotification({
        type: "error",
        title: "Erro ao iniciar consulta",
        message: errorMessage,
        duration: 5000
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleEndConsultation = async () => {
    if (!selectedConsultation) return;

    try {
      setIsLoading(true);
      await consultationService.endConsultation(selectedConsultation.id);
      setSelectedConsultation(null);
      setShowCallModal(false);
      // Só recarregar consultas se necessário
      if (consultations.length > 1) {
        await loadConsultations();
      }

      addNotification({
        type: "success",
        title: "Consulta finalizada",
        message: "A consulta foi finalizada com sucesso",
        duration: 3000
      });
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to end consultation";
      setError(errorMessage);

      addNotification({
        type: "error",
        title: "Erro ao finalizar consulta",
        message: errorMessage,
        duration: 5000
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleRefresh = () => {
    loadConsultations(true);
    if (selectedConsultation) {
      chatHook.refreshMessages();
    }
  };

  const handleBackToDashboard = () => {
    router.push(userRole === "DOCTOR" ? "/app/dashboard" : "/patient/dashboard");
  };

  const handleNewConsultation = () => {
    router.push(userRole === "DOCTOR" ? "/app/appointments" : "/patient/schedule");
  };

  const handleGoToSubscriptions = () => {
    if (userRole === "PATIENT") {
      router.push('/patient/subscriptions');
    }
  };

  const handleGoToOnDuty = () => {
    if (userRole === "PATIENT") {
      router.push('/pay/plantao');
    }
  };

  const handleVideoCall = () => {
    if (!enableVideo || !selectedConsultation) return;
    setCallMode("video");
    setShowCallModal(true);
  };

  const handleAudioCall = () => {
    if (!selectedConsultation) return;
    setCallMode("audio");
    setShowCallModal(true);
  };

  const handleCloseCall = () => {
    setShowCallModal(false);
  };

  const handleOpenMedicalRecord = () => {
    // Implementar abertura do prontuário (placeholder)
    addNotification({
      type: "info",
      title: "Prontuário",
      message: "Funcionalidade em desenvolvimento",
      duration: 3000
    });
  };

  // Memoize consultation data to avoid recreating object on every render
  const consultationData = useMemo(() => {
    if (!selectedConsultation) return null;

    return {
      id: selectedConsultation.id,
      doctor_name: selectedConsultation.doctor_name,
      doctor_avatar: undefined,
      patient_name: selectedConsultation.patient_name,
      patient_avatar: undefined,
      is_on_duty: selectedConsultation.is_on_duty,
    };
  }, [selectedConsultation]);

  // Monitorar status do chat e mostrar notificações
  useEffect(() => {
    if (selectedConsultation && chatHook.connectionStatus) {
      const { connectionStatus, error: chatError } = chatHook;

      // Notificar sobre mudanças de status apenas uma vez
      if (connectionStatus.status === 'connected' && connectionStatus.reconnectAttempts === 0) {
        addNotification({
          type: "success",
          title: "Chat conectado",
          message: "Conectado ao chat em tempo real",
          duration: 2000
        });
      } else if (connectionStatus.status === 'error' && connectionStatus.lastError) {
        addNotification({
          type: "error",
          title: "Erro no chat",
          message: connectionStatus.lastError,
          duration: 5000
        });
      }

      // Notificar sobre erros do chat apenas se for um erro novo
      if (chatError && chatError !== error) {
        addNotification({
          type: "error",
          title: "Erro no chat",
          message: chatError,
          duration: 5000
        });
      }
    }
  }, [selectedConsultation, chatHook.connectionStatus.status, chatHook.error, error]); // Otimizado para evitar loops

  // Só mostrar loading centralizado se não há consultas E está carregando
  if (isLoading && consultations.length === 0) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-2 text-blue-500" />
          <p className="text-gray-600">Carregando suas consultas...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full bg-gray-50 flex flex-col">
      {/* Layout responsivo otimizado */}
      <div className="flex h-full">
        {/* Sidebar - Lista de Consultas - Largura consistente */}
        <div className={cn(
          "transition-all duration-300 bg-white border-r border-gray-200 flex flex-col",
          // Mobile: esconder quando chat selecionado, tela cheia quando não
          isMobile && selectedConsultation
            ? "hidden"
            : isMobile
            ? "w-full"
            : // Desktop: largura fixa sempre - CONSISTENTE
            "w-full lg:w-80 xl:w-96"
        )}>
          {/* Header da Sidebar */}
          <div className="p-3 lg:p-4 border-b border-gray-200 bg-white">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-3">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleBackToDashboard}
                  className="flex items-center gap-2"
                >
                  <ArrowLeft className="w-4 h-4" />
                  {userRole === "DOCTOR" ? "Dashboard" : "Início"}
                </Button>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={handleRefresh}
                disabled={isRefreshing}
                className="flex items-center gap-2"
              >
                <RefreshCw className={`w-4 h-4 ${isRefreshing ? 'animate-spin' : ''}`} />
                Atualizar
              </Button>
            </div>

            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-blue-600 rounded-full flex items-center justify-center">
                <MessageCircle className="w-5 h-5 text-white" />
              </div>
              <div>
                <h1 className="text-lg font-semibold text-gray-900">ZapChat (Atendimentos)</h1>
                <p className="text-sm text-gray-600">
                  {userRole === "DOCTOR" ? "Suas consultas ativas" : "Seus atendimentos"}
                </p>
              </div>
            </div>
          </div>

          {/* Error Display */}
          {error && (
            <div className="p-3 lg:p-4 bg-red-50 border-b border-red-200">
              <div className="flex items-center gap-2">
                <AlertCircle className="w-4 h-4 text-red-500" />
                <p className="text-sm text-red-600">{error}</p>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setError(null)}
                className="mt-2"
              >
                Fechar
              </Button>
            </div>
          )}

          {/* Lista de Consultas */}
          <div className="flex-1 overflow-y-auto">
            <ConsultationList
              consultations={consultations}
              selectedConsultation={selectedConsultation}
              onConsultationSelect={handleConsultationSelect}
              onStartConsultation={handleStartConsultation}
              isLoading={isLoading}
              userRole={userRole.toLowerCase() as 'doctor' | 'patient'}
            />
          </div>
        </div>

        {/* Área Principal - Chat - Layout otimizado */}
        <div className={cn(
          "flex flex-col transition-all duration-300",
          // Mobile: tela cheia quando chat selecionado, escondido quando não
          isMobile && selectedConsultation
            ? "w-full h-full"
            : isMobile
            ? "hidden"
            : // Desktop: sempre visível como no WhatsApp
            "flex-1 min-w-0"
        )}>
          {selectedConsultation && consultationData ? (
            <>

              {/* Interface do Chat - Mobile ou Desktop */}
              <div className="flex-1 overflow-hidden">
                {isMobile ? (
                  <MobileChatInterface
                    consultation={consultationData}
                    userId={user.id}
                    userRole={userRole}
                    messages={chatHook.messages}
                    isLoading={chatHook.isLoading}
                    error={chatHook.error}
                    connectionStatus={chatHook.connectionStatus}
                    isConnected={chatHook.isConnected}
                    isTyping={chatHook.isTyping}
                    typingUsers={chatHook.typingUsers}
                    onSendMessage={chatHook.sendMessage}
                    onSendAudio={chatHook.sendAudio}
                    onSendFile={chatHook.sendFile}
                    onStartTyping={chatHook.startTyping}
                    onStopTyping={chatHook.stopTyping}
                    onRefresh={chatHook.refreshMessages}
                    onReconnect={chatHook.reconnect}
                    onClearError={chatHook.clearError}
                    onBack={() => setSelectedConsultation(null)}
                    onEndConsultation={userRole === "DOCTOR" ? handleEndConsultation : undefined}
                    onVideoCall={userRole === "DOCTOR" && enableVideo ? handleVideoCall : undefined}
                    onAudioCall={userRole === "DOCTOR" ? handleAudioCall : undefined}
                    onOpenMedicalRecord={userRole === "DOCTOR" ? handleOpenMedicalRecord : undefined}
                  />
                ) : (
                  <PremiumChatInterface
                    consultation={consultationData}
                    userId={user.id}
                    userRole={userRole}
                    messages={chatHook.messages}
                    isLoading={chatHook.isLoading}
                    error={chatHook.error}
                    connectionStatus={chatHook.connectionStatus}
                    isConnected={chatHook.isConnected}
                    isTyping={chatHook.isTyping}
                    typingUsers={chatHook.typingUsers}
                    onSendMessage={chatHook.sendMessage}
                    onSendAudio={chatHook.sendAudio}
                    onSendFile={chatHook.sendFile}
                    onStartTyping={chatHook.startTyping}
                    onStopTyping={chatHook.stopTyping}
                    onRefresh={chatHook.refreshMessages}
                    onReconnect={chatHook.reconnect}
                    onClearError={chatHook.clearError}
                    onBack={() => setSelectedConsultation(null)}
                    onEndConsultation={handleEndConsultation}
                    onVideoCall={enableVideo ? handleVideoCall : undefined}
                    onAudioCall={handleAudioCall}
                    onOpenMedicalRecord={userRole === "DOCTOR" ? handleOpenMedicalRecord : undefined}
                  />
                )}
              </div>
            </>
          ) : (
            /* Estado vazio no desktop - Melhorado */
            <div className="flex flex-1 items-center justify-center p-8">
              <div className="text-center max-w-lg">
                <div className="w-24 h-24 mx-auto mb-8 bg-gradient-to-br from-green-100 to-blue-100 rounded-full flex items-center justify-center shadow-lg">
                  <MessageCircle className="w-12 h-12 text-green-600" />
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-4">
                  ZapChat (Atendimentos)
                </h3>
                <p className="text-gray-600 mb-6 text-lg">
                  Suas consultas ativas
                </p>
                <p className="text-gray-500 mb-6">
                  Selecione uma consulta na barra lateral para iniciar o chat em tempo real
                </p>
                {consultations.length === 0 && (
                  <div className="space-y-4">
                    <div className="text-sm text-gray-400 bg-gray-50 rounded-lg p-4">
                      {userRole === "DOCTOR"
                        ? "Você não possui consultas ativas no momento."
                        : "Você não possui consultas agendadas no momento."
                      }
                    </div>
                    {userRole === "PATIENT" && subscriptionSummary && (
                      <div className="flex items-center justify-center">
                        <Badge variant={subscriptionSummary.remainingConsultations > 0 ? "default" : "destructive"} className="text-sm px-4 py-2">
                          Créditos disponíveis: {Math.max(0, subscriptionSummary.remainingConsultations)} de {subscriptionSummary.consultationsIncluded}
                        </Badge>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Modal de Chamadas (Vídeo/Áudio) */}
      {selectedConsultation && (
        <VideoAudioModal
          isOpen={showCallModal}
          onClose={handleCloseCall}
          appointmentId={selectedConsultation.id}
          userRole={userRole}
          mode={callMode}
          otherParticipantName={
            userRole === "DOCTOR" ? selectedConsultation.patient_name || "Paciente" : selectedConsultation.doctor_name || "Médico"
          }
          onError={(error) => {
            console.error("Erro na chamada:", error);
            addNotification({
              type: "error",
              title: `Erro na chamada de ${callMode}`,
              message: error.message,
              duration: 5000
            });
          }}
        />
      )}

      {/* Painel de Debug do Supabase Realtime - Só em desenvolvimento */}
      {/* {process.env.NODE_ENV === 'development' && <SupabaseRealtimeDebugPanel />} */}
    </div>
  );
}

export function NewZapChatClient({ user, enableVideo = true }: NewZapChatClientProps) {
  return (
    <NotificationProvider>
      <ZapChatContent user={user} enableVideo={enableVideo} />
    </NotificationProvider>
  );
}
