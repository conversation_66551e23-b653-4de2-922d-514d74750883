"use client";

import { useState, useMemo } from "react";
import { Consultation } from "@lib/services/consultation.service";
import { ConsultationTabs } from "./consultation-tabs";
// Badge local para simplificar o componente
const Badge = ({ children, className }: { children: React.ReactNode; className?: string }) => (
  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${className}`}>
    {children}
  </span>
);
import { Clock, MessageCircle, User, Stethoscope, AlertTriangle, Zap } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { ptBR } from "date-fns/locale";
import { ON_DUTY_CONFIG } from "api/constants/on-duty";

interface ConsultationListProps {
  consultations: Consultation[];
  selectedConsultation: Consultation | null;
  onConsultationSelect: (consultation: Consultation) => void;
  onStartConsultation: (consultationId: string) => void;
  isLoading: boolean;
  userRole: "doctor" | "patient";
}

export function ConsultationList({
  consultations,
  selectedConsultation,
  onConsultationSelect,
  onStartConsultation,
  isLoading,
  userRole
}: ConsultationListProps) {
  const [activeTab, setActiveTab] = useState('in_progress');

  // Filtrar consultas baseado na tab ativa
  const filteredConsultations = useMemo(() => {
    switch (activeTab) {
      case 'in_progress':
        return consultations.filter(c => c.status === 'IN_PROGRESS' || c.status === 'SCHEDULED');
      case 'on_duty':
        return consultations.filter(c => c.is_on_duty);
      case 'completed':
        return consultations.filter(c => c.status === 'COMPLETED');
      default:
        return consultations;
    }
  }, [consultations, activeTab]);

  const handleTabChange = (tab: string) => {
    setActiveTab(tab);
  };

  const getStatusBadge = (status: string, isOnDuty?: boolean, urgencyLevel?: string | null) => {
    if (isOnDuty) {
      return <Badge className="bg-red-100 text-red-800 flex items-center gap-1">
        <Zap className="w-3 h-3" />
        Plantão
      </Badge>;
    }

    switch (status) {
      case "SCHEDULED":
        return <Badge className="bg-yellow-100 text-yellow-800">Agendada</Badge>;
      case "IN_PROGRESS":
        return <Badge className="bg-green-100 text-green-800">Em andamento</Badge>;
      case "COMPLETED":
        return <Badge className="bg-blue-100 text-blue-800">Finalizada</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800">{status}</Badge>;
    }
  };

  const getTimeAgo = (timestamp: string) => {
    return formatDistanceToNow(new Date(timestamp), {
      addSuffix: true,
      locale: ptBR
    });
  };

  const getDisplayName = (consultation: Consultation) => {
    return userRole === "doctor" ? consultation.patient_name : consultation.doctor_name;
  };

  const getDisplayRole = (consultation: Consultation) => {
    return userRole === "doctor" ? "Paciente" : "Médico";
  };

  const getMessagePreview = (consultation: Consultation) => {
    if (!consultation.last_message) return null;

    const { content, type } = consultation.last_message;

    switch (type) {
      case "TEXT":
        return content.length > 50 ? `${content.substring(0, 50)}...` : content;
      case "AUDIO":
        return "🎵 Mensagem de áudio";
      case "FILE":
        return "📎 Arquivo anexado";
      case "SYSTEM":
        return "ℹ️ " + content;
      default:
        return content;
    }
  };

  // Mapeia o nível de urgência para o rótulo em PT-BR usado no checkout
  const mapUrgencyLabel = (level?: string | null) => {
    if (!level) return null;
    const key = String(level).toUpperCase();
    switch (key) {
      case 'HIGH':
        return ON_DUTY_CONFIG.HIGH.label; // "Muito Urgente"
      case 'MEDIUM':
        return ON_DUTY_CONFIG.MEDIUM.label; // "Urgente"
      case 'LOW':
        return ON_DUTY_CONFIG.LOW.label; // "Pouco Urgente"
      default:
        return level;
    }
  };

  // Mostrar skeleton apenas se está carregando E não há consultas
  if (isLoading && consultations.length === 0) {
    return (
      <div className="flex flex-col h-full">
        {/* Tabs Skeleton */}
        <div className="border-b border-gray-200 bg-white">
          <div className="flex">
            {['Em Andamento', 'Plantão', 'Finalizadas'].map((tab, index) => (
              <div key={tab} className="flex-1 px-4 py-3">
                <div className="flex flex-col items-center gap-1">
                  <div className="flex items-center gap-2">
                    <div className="h-4 bg-gray-200 rounded w-20 animate-pulse" />
                    <div className="h-5 bg-gray-200 rounded-full w-6 animate-pulse" />
                  </div>
                  <div className="h-3 bg-gray-200 rounded w-16 animate-pulse" />
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Lista Skeleton */}
        <div className="flex-1 overflow-y-auto p-3 lg:p-4">
          <div className="space-y-2 lg:space-y-3">
            {[1, 2, 3, 4, 5].map((i) => (
              <div key={i} className="animate-pulse">
                <div className={`p-3 lg:p-4 rounded-lg border cursor-pointer transition-all duration-200 ${
                  i === 1
                    ? 'bg-blue-50 border-blue-200 shadow-sm' // Primeiro item selecionado
                    : 'bg-white border-gray-200 hover:bg-gray-50'
                }`}>
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center gap-2 lg:gap-3 flex-1 min-w-0">
                      <div className="w-8 h-8 lg:w-10 lg:h-10 bg-gray-100 rounded-full flex items-center justify-center shrink-0">
                        <div className="w-4 h-4 lg:w-5 lg:h-5 bg-gray-200 rounded" />
                      </div>
                      <div className="min-w-0 flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <div className="h-4 bg-gray-200 rounded w-24" />
                          {i === 1 && <div className="w-4 h-4 bg-gray-200 rounded" />}
                        </div>
                        <div className="h-3 bg-gray-200 rounded w-16 mb-1" />
                        {i === 1 && (
                          <div className="mt-1">
                            <div className="h-4 bg-gray-200 rounded w-32" />
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="flex flex-col items-end gap-1 shrink-0">
                      <div className="h-5 bg-gray-200 rounded-full w-16" />
                      {i === 1 && <div className="h-4 bg-gray-200 rounded w-4" />}
                    </div>
                  </div>

                  <div className="mb-3">
                    <div className="h-3 bg-gray-200 rounded w-full mb-1" />
                    <div className="h-3 bg-gray-200 rounded w-2/3" />
                    <div className="h-3 bg-gray-200 rounded w-20 mt-1" />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2 text-xs text-gray-500 min-w-0 flex-1">
                      <div className="w-3 h-3 bg-gray-200 rounded shrink-0" />
                      <div className="h-3 bg-gray-200 rounded w-32" />
                    </div>

                    {i > 2 && (
                      <div className="h-6 w-12 bg-gray-200 rounded text-xs shrink-0 ml-2" />
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (!isLoading && consultations.length === 0) {
    return (
      <div className="p-3 lg:p-4">
        <div className="text-center py-8">
          <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
            <MessageCircle className="w-8 h-8 text-gray-400" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Nenhuma consulta ativa
          </h3>
          <p className="text-sm text-gray-500">
            {userRole === "doctor"
              ? "Você não possui consultas agendadas no momento."
              : "Você não possui consultas ativas no momento."}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      {/* Tabs */}
      <ConsultationTabs
        consultations={consultations}
        onTabChange={handleTabChange}
        activeTab={activeTab}
        isLoading={isLoading}
      />

      {/* Lista de Consultas */}
      <div className="flex-1 overflow-y-auto">
        <div className="p-3 lg:p-4">
          <h2 className="text-sm lg:text-base font-medium text-gray-700 mb-3 px-1">
            {activeTab === 'in_progress' && 'Em Andamento'}
            {activeTab === 'on_duty' && 'Plantão'}
            {activeTab === 'completed' && 'Finalizadas'}
            ({filteredConsultations.length})
          </h2>

          <div className="space-y-2 lg:space-y-3">
            {filteredConsultations.map((consultation) => (
            <div
              key={consultation.id}
              onClick={() => onConsultationSelect(consultation)}
              className={`p-3 lg:p-4 rounded-lg border cursor-pointer transition-all duration-200 active:bg-gray-100 ${
                selectedConsultation?.id === consultation.id
                  ? 'bg-blue-50 border-blue-200 shadow-sm'
                  : 'bg-white border-gray-200 hover:bg-gray-50 hover:border-gray-300'
              }`}
            >
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center gap-2 lg:gap-3 flex-1 min-w-0">
                  <div className="w-8 h-8 lg:w-10 lg:h-10 bg-gray-100 rounded-full flex items-center justify-center shrink-0">
                    {userRole === "doctor" ? (
                      <User className="w-4 h-4 lg:w-5 lg:h-5 text-gray-600" />
                    ) : (
                      <Stethoscope className="w-4 h-4 lg:w-5 lg:h-5 text-gray-600" />
                    )}
                  </div>
                  <div className="min-w-0 flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <h3 className="font-medium text-sm lg:text-base text-gray-900 truncate">
                        {getDisplayName(consultation)}
                      </h3>
                      {consultation.is_on_duty && (
                        <AlertTriangle className="w-4 h-4 text-red-500" />
                      )}
                    </div>
                    <p className="text-xs text-gray-500">
                      {getDisplayRole(consultation)}
                    </p>
                    {consultation.urgency_level && (
                      <div className="mt-1">
                        <span className="inline-flex items-center gap-1 text-[10px] font-medium px-2 py-0.5 rounded-full bg-orange-100 text-orange-700">
                          <AlertTriangle className="w-3 h-3" />
                          Urgência: {mapUrgencyLabel(consultation.urgency_level)}
                        </span>
                      </div>
                    )}
                  </div>
                </div>

                <div className="flex flex-col items-end gap-1 shrink-0">
                  {getStatusBadge(consultation.status, consultation.is_on_duty, consultation.urgency_level)}
                  {consultation.unread_count && consultation.unread_count > 0 && (
                    <span className="bg-blue-500 text-white text-xs rounded-full px-2 py-1 min-w-[20px] text-center">
                      {consultation.unread_count}
                    </span>
                  )}
                </div>
              </div>

              {consultation.last_message && (
                <div className="mb-3">
                  <p className="text-xs text-gray-600 line-clamp-2">
                    {getMessagePreview(consultation)}
                  </p>
                  <p className="text-xs text-gray-400 mt-1">
                    {getTimeAgo(consultation.last_message.created_at)}
                  </p>
                </div>
              )}

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2 text-xs text-gray-500 min-w-0 flex-1">
                  <Clock className="w-3 h-3 shrink-0" />
                  <span className="truncate">
                    {consultation.scheduled_at
                      ? `Agendada para ${getTimeAgo(consultation.scheduled_at)}`
                      : "Sem horário definido"
                    }
                  </span>
                </div>

                {consultation.status === "SCHEDULED" && (
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      onStartConsultation(consultation.id);
                    }}
                    className="text-xs shrink-0 ml-2 px-2 py-1 text-gray-700 bg-white border border-gray-300 rounded hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                  >
                    <span className="hidden sm:inline">Iniciar</span>
                    <span className="sm:hidden">▶</span>
                  </button>
                )}
              </div>
            </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
