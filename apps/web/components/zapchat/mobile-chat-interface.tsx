"use client";

import { useState, useRef, useEffect, useCallback } from "react";
import { But<PERSON> } from "@ui/components/button";
import { Avatar, AvatarFallback, AvatarImage } from "@ui/components/avatar";
import { Badge } from "@ui/components/badge";
import {
  Send,
  Mic,
  Plus,
  ArrowLeft,
  Phone,
  Video,
  MoreVertical,
  FileText,
  RefreshCw,
  Settings,
  X,
  Wifi,
  WifiOff,
  Clock,
  AlertCircle,
} from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { ptBR } from "date-fns/locale";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@ui/components/dropdown-menu";
import { toast } from "sonner";

// Tipos
import { ChatMessage, ConnectionStatus, TypingStatus } from "../../lib/services/supabase-realtime.service";
import { cn } from "@ui/lib";

interface MobileChatInterfaceProps {
  consultation: {
    id: string;
    doctor_name?: string;
    doctor_avatar?: string;
    patient_name?: string;
    patient_avatar?: string;
    is_on_duty?: boolean;
  };
  userId: string;
  userRole: 'DOCTOR' | 'PATIENT';
  messages: ChatMessage[];
  isLoading: boolean;
  error: string | null;
  connectionStatus: ConnectionStatus;
  isConnected: boolean;
  isTyping: boolean;
  typingUsers: TypingStatus[];
  onSendMessage: (content: string) => Promise<void>;
  onSendAudio: (audioBlob: Blob) => Promise<void>;
  onSendFile: (file: File) => Promise<void>;
  onStartTyping: () => void;
  onStopTyping: () => void;
  onRefresh: () => Promise<void>;
  onReconnect: () => Promise<void>;
  onClearError: () => void;
  onBack: () => void;
  onEndConsultation?: () => void;
  onVideoCall?: () => void;
  onAudioCall?: () => void;
  onOpenMedicalRecord?: () => void;
}

export function MobileChatInterface({
  consultation,
  userId,
  userRole,
  messages,
  isLoading,
  error,
  connectionStatus,
  isConnected,
  isTyping,
  typingUsers,
  onSendMessage,
  onSendAudio,
  onSendFile,
  onStartTyping,
  onStopTyping,
  onRefresh,
  onReconnect,
  onClearError,
  onBack,
  onEndConsultation,
  onVideoCall,
  onAudioCall,
  onOpenMedicalRecord,
}: MobileChatInterfaceProps) {

  const [inputValue, setInputValue] = useState("");
  const [isRecording, setIsRecording] = useState(false);
  const [mediaRecorder, setMediaRecorder] = useState<MediaRecorder | null>(null);
  const [audioChunks, setAudioChunks] = useState<Blob[]>([]);
  const [playingAudioId, setPlayingAudioId] = useState<string | null>(null);
  const [audioElements, setAudioElements] = useState<Map<string, HTMLAudioElement>>(new Map());
  const [showConnectionDetails, setShowConnectionDetails] = useState(false);

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const recipientName = userRole === 'DOCTOR' ? consultation.patient_name : consultation.doctor_name;
  const recipientAvatar = userRole === 'DOCTOR' ? consultation.patient_avatar : consultation.doctor_avatar;
  const recipientInitial = recipientName?.charAt(0) || (userRole === 'DOCTOR' ? 'P' : 'D');

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  useEffect(() => {
    inputRef.current?.focus();
  }, []);

  useEffect(() => {
    if (typeof window !== "undefined" && navigator.mediaDevices) {
      navigator.mediaDevices
        .getUserMedia({ audio: true })
        .then((stream) => {
          const recorder = new MediaRecorder(stream);
          setMediaRecorder(recorder);

          recorder.ondataavailable = (event) => {
            if (event.data.size > 0) {
              setAudioChunks((prev) => [...prev, event.data]);
            }
          };

          recorder.onstop = async () => {
            const audioBlob = new Blob(audioChunks, { type: "audio/wav" });
            setAudioChunks([]);

            try {
              await onSendAudio(audioBlob);
              toast.success("Áudio enviado com sucesso");
            } catch (error) {
              console.error("Erro ao enviar áudio:", error);
              toast.error("Erro ao enviar áudio");
            }
          };
        })
        .catch((error) => {
          console.error("Erro ao acessar microfone:", error);
          toast.error("Erro ao acessar microfone");
        });
    }
  }, [onSendAudio, audioChunks]);

  const handleSendMessage = async () => {
    if (!inputValue.trim()) return;

    const content = inputValue.trim();
    setInputValue("");
    onStopTyping();

    try {
      await onSendMessage(content);
    } catch (error) {
      console.error("Erro ao enviar mensagem:", error);
      setInputValue(content);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setInputValue(value);

    if (value.trim() && !isTyping) {
      onStartTyping();
    }

    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    typingTimeoutRef.current = setTimeout(() => {
      onStopTyping();
    }, 1000);
  };

  const startRecording = () => {
    if (mediaRecorder && mediaRecorder.state === "inactive") {
      setAudioChunks([]);
      mediaRecorder.start();
      setIsRecording(true);
      toast.info("Gravando áudio...");
    }
  };

  const stopRecording = () => {
    if (mediaRecorder && mediaRecorder.state === "recording") {
      mediaRecorder.stop();
      setIsRecording(false);
    }
  };

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      try {
        await onSendFile(file);
        toast.success("Arquivo enviado com sucesso");
      } catch (error) {
        console.error("Erro ao enviar arquivo:", error);
        toast.error("Erro ao enviar arquivo");
      }
    }
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const toggleAudioPlayback = (audioUrl: string, messageId: string) => {
    const existingAudio = audioElements.get(messageId);

    if (existingAudio) {
      if (playingAudioId === messageId) {
        existingAudio.pause();
        setPlayingAudioId(null);
      } else {
        if (playingAudioId) {
          const previousAudio = audioElements.get(playingAudioId);
          if (previousAudio) {
            previousAudio.pause();
            previousAudio.currentTime = 0;
          }
        }

        existingAudio.play();
        setPlayingAudioId(messageId);
      }
    } else {
      const audio = new Audio(audioUrl);
      audio.onended = () => setPlayingAudioId(null);
      audio.onerror = () => {
        setPlayingAudioId(null);
        toast.error("Erro ao reproduzir áudio");
      };

      setAudioElements(prev => new Map(prev).set(messageId, audio));
      audio.play();
      setPlayingAudioId(messageId);
    }
  };

  const getFileIcon = (fileName: string) => {
    const extension = fileName.split('.').pop()?.toLowerCase();
    if (['mp3', 'wav', 'ogg', 'm4a'].includes(extension || '')) {
      return <FileText className="w-4 h-4" />;
    } else if (['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(extension || '')) {
      return <FileText className="w-4 h-4" />;
    } else {
      return <FileText className="w-4 h-4" />;
    }
  };

  const getConnectionStatusIcon = () => {
    switch (connectionStatus.status) {
      case 'connected':
        return <Wifi className="w-4 h-4 text-green-500" />;
      case 'connecting':
        return <Clock className="w-4 h-4 text-yellow-500 animate-pulse" />;
      case 'reconnecting':
        return <RefreshCw className="w-4 h-4 text-yellow-500 animate-spin" />;
      case 'error':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      default:
        return <WifiOff className="w-4 h-4 text-gray-400" />;
    }
  };

  const getConnectionStatusText = () => {
    const statusText = {
      'connected': 'Conectado',
      'connecting': 'Conectando...',
      'disconnected': 'Desconectado',
      'error': 'Erro de conexão',
      'reconnecting': 'Reconectando...'
    }[connectionStatus.status] || 'Status desconhecido';

    if (connectionStatus.latency) {
      return `${statusText} (${connectionStatus.latency}ms)`;
    }

    return statusText;
  };

  // Evita "em ..." quando timestamps vêm no futuro por fuso/servidor
  const relativeFromNowSafe = (dateInput: string | number | Date) => {
    const original = new Date(dateInput);
    const safe = new Date(Math.min(original.getTime(), Date.now()));
    return formatDistanceToNow(safe, { addSuffix: true, locale: ptBR });
  };

  const getConnectionStatusColor = () => {
    switch (connectionStatus.status) {
      case 'connected':
        return 'text-green-600';
      case 'connecting':
      case 'reconnecting':
        return 'text-yellow-600';
      case 'error':
        return 'text-red-600';
      default:
        return 'text-gray-500';
    }
  };

  return (
    <div className="flex flex-col h-full bg-gradient-to-br from-blue-50 to-indigo-50 min-h-screen">
      {/* Header único para mobile */}
      <div className="bg-white/80 backdrop-blur-sm border-b border-white/20 shadow-sm p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Button
              variant="ghost"
              size="sm"
              onClick={onBack}
              className="shrink-0"
            >
              <ArrowLeft className="w-4 h-4" />
            </Button>

            <Avatar className="w-10 h-10 ring-2 ring-white shadow-sm">
              <AvatarImage src={recipientAvatar} alt={recipientName} />
              <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-600 text-white font-medium">
                {recipientInitial}
              </AvatarFallback>
            </Avatar>

            <div className="min-w-0 flex-1">
              <h2 className="font-semibold text-gray-900 truncate">
                {recipientName || (userRole === 'DOCTOR' ? 'Paciente' : 'Médico')}
              </h2>
              <div className="flex items-center gap-2">
                <Badge
                  className={cn(
                    "text-xs",
                    consultation.is_on_duty ? "bg-blue-500 text-white" : "bg-gray-500 text-white"
                  )}
                >
                  {consultation.is_on_duty ? "Plantão" : "Consulta"}
                </Badge>

                <button
                  onClick={() => setShowConnectionDetails(!showConnectionDetails)}
                  className="flex items-center gap-1 text-xs hover:bg-white/50 rounded px-2 py-1 transition-colors"
                >
                  {getConnectionStatusIcon()}
                  <span className={getConnectionStatusColor()}>
                    {isConnected ? 'Online' : 'Offline'}
                  </span>
                </button>
              </div>
            </div>
          </div>

          {/* Ações do header - apenas para médicos */}
          {userRole === 'DOCTOR' && (
            <div className="flex items-center gap-1">
              {onAudioCall && (
                <Button variant="ghost" size="sm" onClick={onAudioCall}>
                  <Phone className="w-4 h-4" />
                </Button>
              )}

              {onVideoCall && (
                <Button variant="ghost" size="sm" onClick={onVideoCall}>
                  <Video className="w-4 h-4" />
                </Button>
              )}

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm">
                    <MoreVertical className="w-4 h-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  {onOpenMedicalRecord && (
                    <DropdownMenuItem onClick={onOpenMedicalRecord}>
                      <FileText className="w-4 h-4 mr-2" />
                      Prontuário
                    </DropdownMenuItem>
                  )}
                  <DropdownMenuItem onClick={onRefresh}>
                    <RefreshCw className="w-4 h-4 mr-2" />
                    Atualizar
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={onReconnect}>
                    <Settings className="w-4 h-4 mr-2" />
                    Reconectar
                  </DropdownMenuItem>
                  {onEndConsultation && (
                    <DropdownMenuItem onClick={onEndConsultation} className="text-red-600">
                      <X className="w-4 h-4 mr-2" />
                      Finalizar Consulta
                    </DropdownMenuItem>
                  )}
                </DropdownMenuContent>
              </DropdownMenu>
              {onEndConsultation && (
                <Button size="sm" className="bg-red-600 hover:bg-red-700 text-white">
                  Finalizar
                </Button>
              )}
            </div>
          )}
        </div>

        {/* Detalhes da conexão expandidos */}
        {showConnectionDetails && (
          <div className="mt-4 p-3 bg-white/60 rounded-lg border border-white/30 text-sm">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <span className="font-medium text-gray-700">Status:</span>
                <div className="flex items-center gap-2 mt-1">
                  {getConnectionStatusIcon()}
                  <span className={getConnectionStatusColor()}>
                    {getConnectionStatusText()}
                  </span>
                </div>
              </div>
              <div>
                <span className="font-medium text-gray-700">Tentativas:</span>
                <span className="block mt-1 text-gray-600">
                  {connectionStatus.reconnectAttempts}
                </span>
              </div>
              {connectionStatus.lastConnected && (
                <div className="col-span-2">
                  <span className="font-medium text-gray-700">Última conexão:</span>
                  <span className="block mt-1 text-gray-600">
                    {formatDistanceToNow(connectionStatus.lastConnected, {
                      addSuffix: true,
                      locale: ptBR
                    })}
                  </span>
                </div>
              )}
              {connectionStatus.lastError && (
                <div className="col-span-2">
                  <span className="font-red-600">Erro:</span>
                  <span className="block mt-1 text-red-600 text-xs">
                    {connectionStatus.lastError}
                  </span>
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Área de mensagens */}
      <div className="flex-1 overflow-y-auto p-4 pb-28 space-y-4 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-transparent">
        {isLoading && messages.length === 0 ? (
          <div className="flex items-center justify-center h-32">
            <div className="text-center">
              <RefreshCw className="w-8 h-8 animate-spin mx-auto mb-2 text-blue-500" />
              <p className="text-gray-600">Carregando mensagens...</p>
            </div>
          </div>
        ) : messages.length === 0 ? (
          <div className="text-center py-12">
            <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-blue-100 to-purple-100 rounded-full flex items-center justify-center">
              <Send className="w-8 h-8 text-blue-500" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Inicie a conversa
            </h3>
            <p className="text-gray-500">
              Envie uma mensagem para começar o atendimento
            </p>
          </div>
        ) : (
          messages.map((message) => {
            const isOwnMessage = message.senderId === userId;
            const isAudio = message.type === 'AUDIO';
            const isFile = message.type === 'FILE';
            const isSystem = message.type === 'SYSTEM';

            if (isSystem) {
              return (
                <div key={message.id} className="flex justify-center">
                  <Badge className="text-xs bg-white/70 text-gray-600">
                    {message.content}
                  </Badge>
                </div>
              );
            }

            return (
              <div
                key={message.id}
                className={cn(
                  "flex gap-3",
                  isOwnMessage ? "flex-row-reverse" : "flex-row"
                )}
              >
                <Avatar className="w-8 h-8 shrink-0">
                  <AvatarImage src={isOwnMessage ? undefined : recipientAvatar} />
                  <AvatarFallback className="text-xs bg-gradient-to-br from-blue-500 to-purple-600 text-white">
                    {isOwnMessage ? 'V' : recipientInitial}
                  </AvatarFallback>
                </Avatar>

                <div
                  className={cn(
                    "max-w-[80%] rounded-2xl px-4 py-2",
                    isOwnMessage
                      ? "bg-blue-500 text-white rounded-br-md"
                      : "bg-white text-gray-900 rounded-bl-md shadow-sm"
                  )}
                >
                  {isAudio ? (
                    <div className="flex items-center gap-2">
                      <button
                        onClick={() => toggleAudioPlayback(message.content, message.id)}
                        className="flex items-center gap-2 hover:opacity-80 transition-opacity"
                      >
                        {playingAudioId === message.id ? (
                          <div className="w-4 h-4 bg-current rounded-full animate-pulse" />
                        ) : (
                          <div className="w-4 h-4 bg-current rounded-full" />
                        )}
                        <span className="text-sm">Áudio</span>
                      </button>
                    </div>
                  ) : isFile ? (
                    <div className="flex items-center gap-2">
                      {getFileIcon(message.content)}
                      <span className="text-sm truncate">
                        {message.content.split('/').pop() || 'Arquivo'}
                      </span>
                    </div>
                  ) : (
                    <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                  )}

                  <div
                    className={cn(
                      "text-xs mt-1",
                      isOwnMessage ? "text-blue-100" : "text-gray-500"
                    )}
                  >
                    {relativeFromNowSafe(message.createdAt)}
                  </div>
                </div>
              </div>
            );
          })
        )}

        {/* Indicador de digitação */}
        {typingUsers.length > 0 && (
          <div className="flex gap-3">
            <Avatar className="w-8 h-8 shrink-0">
              <AvatarFallback className="text-xs bg-gradient-to-br from-blue-500 to-purple-600 text-white">
                {recipientInitial}
              </AvatarFallback>
            </Avatar>
            <div className="bg-white text-gray-900 rounded-2xl rounded-bl-md shadow-sm px-4 py-2">
              <div className="flex items-center gap-1">
                <div className="flex gap-1">
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" />
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
                </div>
                <span className="text-xs text-gray-500 ml-2">Digitando...</span>
              </div>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Input fixo no final da página (mobile) */}
      <div className="sticky bottom-0 z-10 bg-gray-100/90 backdrop-blur-md border-t border-gray-300 p-4 safe-area-bottom">
        <div className="flex items-center gap-3 max-w-3xl mx-auto">
          {/* Botão de anexo */}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => fileInputRef.current?.click()}
            className="shrink-0 w-10 h-10 p-0 rounded-full bg-gray-200 hover:bg-gray-300 transition-all duration-200"
          >
            <Plus className="w-5 h-5 text-gray-700" />
          </Button>

          {/* Input de texto - design similar ao da imagem */}
          <div className="flex-1 relative">
            <input
              ref={inputRef}
              type="text"
              value={inputValue}
              onChange={handleInputChange}
              onKeyPress={handleKeyPress}
              placeholder="Digite sua mensagem..."
              className="w-full px-4 py-3 bg-gray-200 rounded-full text-gray-900 placeholder-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:bg-white transition-all duration-200 shadow-sm"
            />
          </div>

          {/* Botão de áudio */}
          <Button
            variant="ghost"
            size="sm"
            onMouseDown={startRecording}
            onMouseUp={stopRecording}
            onTouchStart={startRecording}
            onTouchEnd={stopRecording}
            className={cn(
              "shrink-0 w-10 h-10 p-0 rounded-full transition-all duration-200",
              isRecording
                ? "bg-red-500 hover:bg-red-600 text-white shadow-lg"
                : "bg-gray-200 hover:bg-gray-300 text-gray-700"
            )}
          >
            <Mic className="w-5 h-5" />
          </Button>

          {/* Botão de envio */}
          <Button
            onClick={handleSendMessage}
            disabled={!inputValue.trim()}
            className="shrink-0 w-10 h-10 p-0 rounded-full bg-blue-600 hover:bg-blue-700 text-white disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-sm"
          >
            <Send className="w-5 h-5" />
          </Button>
        </div>

        {/* Input de arquivo oculto */}
        <input
          ref={fileInputRef}
          type="file"
          onChange={handleFileSelect}
          className="hidden"
          accept="image/*,audio/*,video/*,.pdf,.doc,.docx,.txt"
        />
      </div>
    </div>
  );
}
