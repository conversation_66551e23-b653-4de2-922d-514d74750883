import { db } from 'database';

/**
 * Verifica se o paciente tem uma assinatura ativa e pode usar consultas
 */
export async function checkSubscriptionConsultationRights(patientId: string) {
  // Buscar assinatura ativa do paciente
  const subscription = await db.patientSubscription.findFirst({
    where: {
      patientId,
      status: 'ACTIVE',
    },
    select: {
      id: true,
      consultationsIncluded: true,
      consultationsUsed: true,
      lastResetDate: true,
      planName: true,
      status: true,
    },
  });

  if (!subscription) {
    return {
      hasActiveSubscription: false,
      canUseConsultation: false,
      remainingConsultations: 0,
      subscriptionInfo: null,
    };
  }

  // Verificar se precisa resetar consultas (novo mês)
  const now = new Date();
  const lastReset = new Date(subscription.lastResetDate);
  const shouldReset =
    now.getFullYear() > lastReset.getFullYear() ||
    (now.getFullYear() === lastReset.getFullYear() && now.getMonth() > lastReset.getMonth());

  let currentUsed = subscription.consultationsUsed;

  if (shouldReset) {
    // Reset consultas para novo mês
    await db.patientSubscription.update({
      where: { id: subscription.id },
      data: {
        consultationsUsed: 0,
        lastResetDate: new Date(now.getFullYear(), now.getMonth(), 1), // Primeiro dia do mês
      },
    });
    currentUsed = 0;
  }

  const remainingConsultations = Math.max(0, subscription.consultationsIncluded - currentUsed);
  const canUseConsultation = remainingConsultations > 0;

  return {
    hasActiveSubscription: true,
    canUseConsultation,
    remainingConsultations,
    subscriptionInfo: {
      id: subscription.id,
      planName: subscription.planName,
      consultationsIncluded: subscription.consultationsIncluded,
      consultationsUsed: currentUsed,
    },
  };
}

/**
 * Registra o uso de uma consulta na assinatura
 */
export async function recordConsultationUsage(
  appointmentId: string,
  patientId: string,
  type: 'SUBSCRIPTION' | 'SINGLE_PAYMENT' | 'ON_DUTY' = 'SUBSCRIPTION'
) {
  const subscription = await db.patientSubscription.findFirst({
    where: {
      patientId,
      status: 'ACTIVE',
    },
  });

  if (!subscription) {
    throw new Error('Paciente não possui assinatura ativa');
  }

  // Verificar se ainda tem consultas disponíveis (otimizado)
  const remainingConsultations = Math.max(0, subscription.consultationsIncluded - subscription.consultationsUsed);
  if (remainingConsultations <= 0 && type === 'SUBSCRIPTION') {
    throw new Error(`Limite de consultas atingido para este mês. Restam: ${remainingConsultations}`);
  }

  // Criar registro de uso
  await db.consultationUsage.create({
    data: {
      patientId,
      subscriptionId: subscription.id,
      appointmentId,
      type,
      usedAt: new Date(),
    },
  });

  // Incrementar contador de consultas usadas (apenas para tipo SUBSCRIPTION)
  if (type === 'SUBSCRIPTION') {
    await db.patientSubscription.update({
      where: { id: subscription.id },
      data: {
        consultationsUsed: {
          increment: 1,
        },
      },
    });
  }

  return {
    success: true,
    remainingConsultations: remainingConsultations - (type === 'SUBSCRIPTION' ? 1 : 0),
  };
}

/**
 * Libera uma consulta (em caso de cancelamento)
 */
export async function releaseConsultationUsage(appointmentId: string) {
  const usage = await db.consultationUsage.findFirst({
    where: { appointmentId },
    include: { patientSubscription: true },
  });

  if (!usage || !usage.patientSubscription) {
    return { success: false, message: 'Uso de consulta não encontrado' };
  }

  // Remover registro de uso
  await db.consultationUsage.delete({
    where: { id: usage.id },
  });

  // Decrementar contador se foi consulta de assinatura
  if (usage.type === 'SUBSCRIPTION') {
    await db.patientSubscription.update({
      where: { id: usage.patientSubscription.id },
      data: {
        consultationsUsed: {
          decrement: 1,
        },
      },
    });
  }

  return { success: true, message: 'Consulta liberada com sucesso' };
}

/**
 * Verifica o limite de consultas mensais para um paciente
 */
export async function getMonthlyConsultationSummary(patientId: string) {
  const subscription = await db.patientSubscription.findFirst({
    where: {
      patientId,
      status: 'ACTIVE',
    },
    include: {
      consultationUsages: {
        where: {
          usedAt: {
            gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1), // Início do mês atual
          },
        },
        include: {
          appointment: {
            select: {
              id: true,
              scheduledAt: true,
              status: true,
              doctor: {
                select: {
                  user: { select: { name: true } },
                },
              },
            },
          },
        },
        orderBy: { usedAt: 'desc' },
      },
    },
  });

  if (!subscription) {
    return {
      hasSubscription: false,
      consultationsIncluded: 0,
      consultationsUsed: 0,
      remainingConsultations: 0,
      usageHistory: [],
    };
  }

  return {
    hasSubscription: true,
    consultationsIncluded: subscription.consultationsIncluded,
    consultationsUsed: subscription.consultationsUsed,
    remainingConsultations: Math.max(0, subscription.consultationsIncluded - subscription.consultationsUsed),
    usageHistory: subscription.consultationUsages,
    planName: subscription.planName,
  };
}
