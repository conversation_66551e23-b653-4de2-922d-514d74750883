/**
 * Configuração de variáveis de ambiente
 * Este arquivo verifica se as variáveis necessárias estão disponíveis
 */

// Importar o carregador de variáveis de ambiente
import './env-loader';
import './server-env';

export interface EnvConfig {
  supabase: {
    url: string;
    anonKey: string;
    serviceRoleKey?: string;
  };
  app: {
    url: string;
    env: string;
  };
  database: {
    url: string;
    directUrl: string;
  };
}

function getRequiredEnvVar(name: string): string {
  const value = process.env[name];
  if (!value) {
    throw new Error(`Variável de ambiente obrigatória não encontrada: ${name}`);
  }
  return value;
}

function getOptionalEnvVar(name: string): string | undefined {
  return process.env[name];
}

export function getEnvConfig(): EnvConfig {
  try {
    return {
      supabase: {
        url: getRequiredEnvVar('NEXT_PUBLIC_SUPABASE_URL'),
        anonKey: getRequiredEnvVar('NEXT_PUBLIC_SUPABASE_ANON_KEY'),
        serviceRoleKey: getOptionalEnvVar('SUPABASE_SERVICE_ROLE_KEY'),
      },
      app: {
        url: getOptionalEnvVar('NEXT_PUBLIC_SITE_URL') || 'http://localhost:3000',
        env: getOptionalEnvVar('NODE_ENV') || 'development',
      },
      database: {
        url: getRequiredEnvVar('DATABASE_URL'),
        directUrl: getRequiredEnvVar('DIRECT_URL'),
      },
    };
  } catch (error) {
    console.error('Erro ao carregar configuração de ambiente:', error);
    throw error;
  }
}

export function validateEnvConfig(): boolean {
  try {
    getEnvConfig();
    return true;
  } catch {
    return false;
  }
}

export function getSupabaseConfig() {
  const config = getEnvConfig();
  return {
    url: config.supabase.url,
    anonKey: config.supabase.anonKey,
    serviceRoleKey: config.supabase.serviceRoleKey,
  };
}

// Log das variáveis de ambiente em desenvolvimento
if (process.env.NODE_ENV === 'development') {
  console.log('🔧 Configuração de ambiente carregada:', {
    NEXT_PUBLIC_SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL ? '✓' : '✗',
    NEXT_PUBLIC_SUPABASE_ANON_KEY: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? '✓' : '✗',
    NEXT_PUBLIC_SITE_URL: process.env.NEXT_PUBLIC_SITE_URL ? '✓' : '✗',
    SUPABASE_SERVICE_ROLE_KEY: process.env.SUPABASE_SERVICE_ROLE_KEY ? '✓' : '✗',
    NODE_ENV: process.env.NODE_ENV,
  });
}
