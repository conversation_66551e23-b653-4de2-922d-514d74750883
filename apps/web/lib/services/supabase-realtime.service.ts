import { createClient, SupabaseClient, RealtimeChannel, RealtimePostgresChangesPayload } from '@supabase/supabase-js';
import { getSupabaseConfig } from '../config/supabase.config';

// Tipos para o serviço de chat
export interface ChatMessage {
  id: string;
  content: string;
  type: 'TEXT' | 'AUDIO' | 'FILE' | 'SYSTEM';
  senderId: string;
  appointmentId: string;
  createdAt: string;
  metadata?: Record<string, any>;
  senderRole?: string;
  file_url?: string;
  file_name?: string;
  file_size?: number;
}

export interface ConnectionStatus {
  status: 'connecting' | 'connected' | 'disconnected' | 'error' | 'reconnecting';
  lastConnected?: Date;
  reconnectAttempts: number;
  lastError?: string;
  isOnline: boolean;
  latency?: number;
}

export interface TypingStatus {
  userId: string;
  userName: string;
  isTyping: boolean;
  timestamp: number;
}

type MessageHandler = (message: ChatMessage) => void;
type StatusHandler = (status: ConnectionStatus) => void;
type TypingHandler = (typing: TypingStatus[]) => void;

/**
 * Serviço limpo e eficiente para Supabase Realtime
 * Usa apenas o SDK oficial do Supabase, sem fallbacks complexos
 */
export class SupabaseRealtimeService {
  private client;
  private channels = new Map<string, RealtimeChannel>();
  private connectionStatus: ConnectionStatus = {
    status: 'disconnected',
    reconnectAttempts: 0,
    isOnline: navigator?.onLine ?? true,
  };

  private messageHandlers = new Map<string, MessageHandler[]>();
  private statusHandlers = new Map<string, StatusHandler[]>();
  private typingHandlers = new Map<string, TypingHandler[]>();
  private typingTimeouts = new Map<string, NodeJS.Timeout>();
  private activeTypingUsers = new Map<string, TypingStatus[]>();

  // Controle de reconexão
  private reconnectTimeouts = new Map<string, NodeJS.Timeout>();
  private maxReconnectAttempts = 5;
  private baseReconnectDelay = 1000; // 1 segundo

  // Monitoramento de latência
  private pingInterval?: NodeJS.Timeout;
  private lastPingTime?: number;

  constructor() {
    const config = getSupabaseConfig();
    this.client = createClient(
      config.url,
      config.anonKey,
      {
        realtime: {
          params: {
            eventsPerSecond: 10, // Otimizado para estabilidade
          },
          // Configurações melhoradas para estabilidade
          config: {
            presence: {
              key: 'zapvida-chat'
            },
            broadcast: {
              self: false
            }
          },
          // Heartbeat e timeout otimizados
          heartbeatIntervalMs: 30000,
          reconnectAfterMs: (tries: number) => Math.min(tries * 1000, 10000),
          timeout: 15000
        },
        // Configurações otimizadas para estabilidade
        auth: {
          autoRefreshToken: true,
          persistSession: true,
          detectSessionInUrl: false,
        },
        global: {
          headers: {
            'x-client-info': 'zapvida-chat-v2',
          },
        },
      }
    );

    this.setupOnlineStatusMonitoring();
    this.startLatencyMonitoring();
    this.setupConnectionMonitoring();
  }

  /**
   * Conecta a um chat específico
   */
  async connectToChat(appointmentId: string, userId: string): Promise<void> {
    console.log(`[SupabaseRealtime] Conectando ao chat ${appointmentId} para usuário ${userId}`);

    // Verificar se já existe um canal para este appointment
    const existingChannel = this.channels.get(appointmentId);
    if (existingChannel) {
      console.log(`[SupabaseRealtime] Canal já existe para ${appointmentId}, removendo primeiro`);
      await this.disconnectFromChat(appointmentId);
    }

    this.updateStatus(appointmentId, 'connecting');

    try {
      const channelName = `chat:${appointmentId}`;
      console.log(`[SupabaseRealtime] Criando canal: ${channelName}`);

      const channel = this.client
        .channel(channelName, {
          config: {
            presence: { key: userId },
            broadcast: { self: true },
          },
        })
        .on('postgres_changes', {
          event: 'INSERT',
          schema: 'public',
          table: 'messages',
          filter: `appointmentId=eq.${appointmentId}`,
        }, (payload: RealtimePostgresChangesPayload<any>) => {
          console.log(`[SupabaseRealtime] Nova mensagem recebida:`, payload.new);
          this.handleNewMessage(appointmentId, payload.new);
        })
        .on('broadcast', { event: 'typing' }, (payload) => {
          console.log(`[SupabaseRealtime] Evento de digitação:`, payload);
          this.handleTypingEvent(appointmentId, payload.payload);
        })
        .on('presence', { event: 'sync' }, () => {
          console.log(`[SupabaseRealtime] Presença sincronizada para ${appointmentId}`);
          const state = channel.presenceState();
          console.log('Estado de presença:', state);
        })
        .subscribe((status) => {
          console.log(`[SupabaseRealtime] Status de inscrição: ${status} para ${appointmentId}`);

          switch (status) {
            case 'SUBSCRIBED':
              this.updateStatus(appointmentId, 'connected');
              this.connectionStatus.reconnectAttempts = 0;
              break;
            case 'CHANNEL_ERROR':
              this.updateStatus(appointmentId, 'error', 'Erro no canal');
              // Só agendar reconexão se não excedeu o limite
              if (this.connectionStatus.reconnectAttempts < this.maxReconnectAttempts) {
                this.scheduleReconnect(appointmentId, userId);
              }
              break;
            case 'TIMED_OUT':
              this.updateStatus(appointmentId, 'error', 'Timeout na conexão');
              // Só agendar reconexão se não excedeu o limite
              if (this.connectionStatus.reconnectAttempts < this.maxReconnectAttempts) {
                this.scheduleReconnect(appointmentId, userId);
              }
              break;
            case 'CLOSED':
              this.updateStatus(appointmentId, 'disconnected');
              break;
          }
        });

      this.channels.set(appointmentId, channel);

      // Aguardar um momento para a conexão se estabelecer
      await new Promise(resolve => setTimeout(resolve, 1000));

      console.log(`[SupabaseRealtime] Canal criado e inscrito para ${appointmentId}`);

    } catch (error) {
      console.error(`[SupabaseRealtime] Erro ao conectar ao chat:`, error);
      this.updateStatus(appointmentId, 'error', error instanceof Error ? error.message : 'Erro desconhecido');
      // Não agendar reconexão automática para erros de conexão inicial
      // Apenas atualizar o status e deixar o hook decidir se deve reconectar
      throw error;
    }
  }

  /**
   * Desconecta de um chat específico
   */
  async disconnectFromChat(appointmentId: string): Promise<void> {
    console.log(`[SupabaseRealtime] Desconectando do chat ${appointmentId}`);

    const channel = this.channels.get(appointmentId);
    if (channel) {
      await this.client.removeChannel(channel);
      this.channels.delete(appointmentId);
    }

    // Limpar timeouts e handlers
    const reconnectTimeout = this.reconnectTimeouts.get(appointmentId);
    if (reconnectTimeout) {
      clearTimeout(reconnectTimeout);
      this.reconnectTimeouts.delete(appointmentId);
    }

    // Limpar dados de digitação
    this.activeTypingUsers.delete(appointmentId);

    this.updateStatus(appointmentId, 'disconnected');

    // Limpar handlers
    this.messageHandlers.delete(appointmentId);
    this.statusHandlers.delete(appointmentId);
    this.typingHandlers.delete(appointmentId);
  }

  /**
   * Envia uma mensagem de digitação
   */
  async sendTypingIndicator(appointmentId: string, userId: string, userName: string, isTyping: boolean): Promise<void> {
    const channel = this.channels.get(appointmentId);
    if (!channel) {
      console.warn(`[SupabaseRealtime] Canal não encontrado para ${appointmentId}`);
      return;
    }

    const payload = {
      userId,
      userName,
      isTyping,
      timestamp: Date.now(),
    };

    try {
      await channel.send({
        type: 'broadcast',
        event: 'typing',
        payload,
      });
      console.log(`[SupabaseRealtime] Indicador de digitação enviado:`, payload);
    } catch (error) {
      console.error(`[SupabaseRealtime] Erro ao enviar indicador de digitação:`, error);
    }
  }

  /**
   * Verifica a latência da conexão
   */
  async pingConnection(appointmentId: string): Promise<number | null> {
    const channel = this.channels.get(appointmentId);
    if (!channel) return null;

    const startTime = Date.now();
    this.lastPingTime = startTime;

    try {
      await channel.send({
        type: 'broadcast',
        event: 'ping',
        payload: { timestamp: startTime },
      });

      return Date.now() - startTime;
    } catch (error) {
      console.error(`[SupabaseRealtime] Erro no ping:`, error);
      return null;
    }
  }

  /**
   * Subscreve a mensagens
   */
  onMessage(appointmentId: string, handler: MessageHandler): () => void {
    if (!this.messageHandlers.has(appointmentId)) {
      this.messageHandlers.set(appointmentId, []);
    }

    this.messageHandlers.get(appointmentId)!.push(handler);

    return () => {
      const handlers = this.messageHandlers.get(appointmentId);
      if (handlers) {
        const index = handlers.indexOf(handler);
        if (index > -1) {
          handlers.splice(index, 1);
        }
        if (handlers.length === 0) {
          this.messageHandlers.delete(appointmentId);
        }
      }
    };
  }

  /**
   * Subscreve a mudanças de status
   */
  onStatusChange(appointmentId: string, handler: StatusHandler): () => void {
    if (!this.statusHandlers.has(appointmentId)) {
      this.statusHandlers.set(appointmentId, []);
    }

    this.statusHandlers.get(appointmentId)!.push(handler);

    // Enviar status atual imediatamente
    handler(this.connectionStatus);

    return () => {
      const handlers = this.statusHandlers.get(appointmentId);
      if (handlers) {
        const index = handlers.indexOf(handler);
        if (index > -1) {
          handlers.splice(index, 1);
        }
        if (handlers.length === 0) {
          this.statusHandlers.delete(appointmentId);
        }
      }
    };
  }

  /**
   * Subscreve a eventos de digitação
   */
  onTypingChange(appointmentId: string, handler: TypingHandler): () => void {
    if (!this.typingHandlers.has(appointmentId)) {
      this.typingHandlers.set(appointmentId, []);
    }

    this.typingHandlers.get(appointmentId)!.push(handler);

    return () => {
      const handlers = this.typingHandlers.get(appointmentId);
      if (handlers) {
        const index = handlers.indexOf(handler);
        if (index > -1) {
          handlers.splice(index, 1);
        }
        if (handlers.length === 0) {
          this.typingHandlers.delete(appointmentId);
        }
      }
    };
  }

  /**
   * Obtém o status atual da conexão
   */
  getConnectionStatus(): ConnectionStatus {
    return { ...this.connectionStatus };
  }

  /**
   * Força uma reconexão
   */
  async reconnect(appointmentId: string, userId: string): Promise<void> {
    console.log(`[SupabaseRealtime] Reconectando ${appointmentId}...`);
    await this.disconnectFromChat(appointmentId);
    await this.connectToChat(appointmentId, userId);
  }

  /**
   * Destroi o serviço e limpa todos os recursos
   */
  async destroy(): Promise<void> {
    console.log('[SupabaseRealtime] Destruindo serviço...');

    // Parar monitoramento de latência
    if (this.pingInterval) {
      clearInterval(this.pingInterval);
    }

    // Desconectar todos os chats
    const appointmentIds = Array.from(this.channels.keys());
    for (const appointmentId of appointmentIds) {
      await this.disconnectFromChat(appointmentId);
    }

    // Limpar todos os timeouts
    this.reconnectTimeouts.forEach((timeout) => {
      clearTimeout(timeout);
    });
    this.reconnectTimeouts.clear();

    this.typingTimeouts.forEach((timeout) => {
      clearTimeout(timeout);
    });
    this.typingTimeouts.clear();

    // Limpar maps
    this.channels.clear();
    this.messageHandlers.clear();
    this.statusHandlers.clear();
    this.typingHandlers.clear();
    this.activeTypingUsers.clear();
  }

  // Métodos privados
  private handleNewMessage(appointmentId: string, messageData: any): void {
    if (!messageData || !messageData.id) {
      console.warn('[SupabaseRealtime] Mensagem inválida recebida:', messageData);
      return;
    }

    const message: ChatMessage = {
      id: messageData.id,
      content: messageData.content,
      type: messageData.type,
      senderId: messageData.senderId,
      appointmentId: messageData.appointmentId,
      createdAt: messageData.createdAt,
      metadata: messageData.metadata,
      senderRole: messageData.senderRole,
      file_url: messageData.file_url,
      file_name: messageData.file_name,
      file_size: messageData.file_size,
    };

    const handlers = this.messageHandlers.get(appointmentId);
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(message);
        } catch (error) {
          console.error('[SupabaseRealtime] Erro ao processar handler de mensagem:', error);
        }
      });
    }
  }

  private handleTypingEvent(appointmentId: string, payload: any): void {
    if (!payload || !payload.userId) {
      console.warn('[SupabaseRealtime] Evento de digitação inválido:', payload);
      return;
    }

    const typingStatus: TypingStatus = {
      userId: payload.userId,
      userName: payload.userName || 'Usuário',
      isTyping: payload.isTyping,
      timestamp: payload.timestamp || Date.now(),
    };

    // Atualizar lista de usuários digitando
    let activeTyping = this.activeTypingUsers.get(appointmentId) || [];

    if (typingStatus.isTyping) {
      // Adicionar ou atualizar usuário digitando
      const existingIndex = activeTyping.findIndex(t => t.userId === typingStatus.userId);
      if (existingIndex >= 0) {
        activeTyping[existingIndex] = typingStatus;
      } else {
        activeTyping.push(typingStatus);
      }

      // Limpar timeout anterior
      const timeoutKey = `${appointmentId}:${typingStatus.userId}`;
      const existingTimeout = this.typingTimeouts.get(timeoutKey);
      if (existingTimeout) {
        clearTimeout(existingTimeout);
      }

      // Configurar novo timeout para parar digitação
      const timeout = setTimeout(() => {
        this.handleTypingEvent(appointmentId, {
          ...payload,
          isTyping: false,
        });
      }, 3000); // Parar digitação após 3 segundos de inatividade

      this.typingTimeouts.set(timeoutKey, timeout);
    } else {
      // Remover usuário da lista de digitação
      activeTyping = activeTyping.filter(t => t.userId !== typingStatus.userId);

      // Limpar timeout
      const timeoutKey = `${appointmentId}:${typingStatus.userId}`;
      const existingTimeout = this.typingTimeouts.get(timeoutKey);
      if (existingTimeout) {
        clearTimeout(existingTimeout);
        this.typingTimeouts.delete(timeoutKey);
      }
    }

    this.activeTypingUsers.set(appointmentId, activeTyping);

    // Notificar handlers
    const handlers = this.typingHandlers.get(appointmentId);
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(activeTyping);
        } catch (error) {
          console.error('[SupabaseRealtime] Erro ao processar handler de digitação:', error);
        }
      });
    }
  }

  private updateStatus(appointmentId: string, status: ConnectionStatus['status'], error?: string): void {
    const previousStatus = this.connectionStatus.status;

    this.connectionStatus.status = status;
    this.connectionStatus.isOnline = navigator?.onLine ?? true;

    if (error) {
      this.connectionStatus.lastError = error;
    }

    if (status === 'connected') {
      this.connectionStatus.lastConnected = new Date();
      this.connectionStatus.lastError = undefined;
    }

    // Notificar apenas se o status mudou
    if (previousStatus !== status) {
      console.log(`[SupabaseRealtime] Status atualizado: ${previousStatus} -> ${status}`);

      const handlers = this.statusHandlers.get(appointmentId);
      if (handlers) {
        handlers.forEach(handler => {
          try {
            handler({ ...this.connectionStatus });
          } catch (error) {
            console.error('[SupabaseRealtime] Erro ao processar handler de status:', error);
          }
        });
      }
    }
  }

  private scheduleReconnect(appointmentId: string, userId: string): void {
    if (this.connectionStatus.reconnectAttempts >= this.maxReconnectAttempts) {
      console.warn(`[SupabaseRealtime] Máximo de tentativas excedido para ${appointmentId}`);
      this.updateStatus(appointmentId, 'error', 'Máximo de tentativas de reconexão excedido');
      return;
    }

    // Limpar timeout anterior se existir
    const existingTimeout = this.reconnectTimeouts.get(appointmentId);
    if (existingTimeout) {
      clearTimeout(existingTimeout);
    }

    this.connectionStatus.reconnectAttempts += 1;
    const delay = this.baseReconnectDelay * Math.pow(2, this.connectionStatus.reconnectAttempts - 1);

    console.log(`[SupabaseRealtime] Agendando reconexão em ${delay}ms (tentativa ${this.connectionStatus.reconnectAttempts})`);

    this.updateStatus(appointmentId, 'reconnecting');

    const timeout = setTimeout(async () => {
      try {
        await this.reconnect(appointmentId, userId);
      } catch (error) {
        console.error(`[SupabaseRealtime] Erro na reconexão:`, error);
        // Só agendar nova reconexão se não excedeu o limite
        if (this.connectionStatus.reconnectAttempts < this.maxReconnectAttempts) {
          this.scheduleReconnect(appointmentId, userId);
        } else {
          this.updateStatus(appointmentId, 'error', 'Falha na reconexão após múltiplas tentativas');
        }
      }
    }, delay);

    this.reconnectTimeouts.set(appointmentId, timeout);
  }

  private setupOnlineStatusMonitoring(): void {
    if (typeof window === 'undefined') return;

    const updateOnlineStatus = () => {
      const wasOnline = this.connectionStatus.isOnline;
      this.connectionStatus.isOnline = navigator.onLine;

      if (wasOnline && !this.connectionStatus.isOnline) {
        console.log('[SupabaseRealtime] Conexão offline detectada');
        // Notificar todos os chats sobre desconexão
        this.channels.forEach((_, appointmentId) => {
          this.updateStatus(appointmentId, 'disconnected');
        });
      } else if (!wasOnline && this.connectionStatus.isOnline) {
        console.log('[SupabaseRealtime] Conexão online restaurada');
        // Tentar reconectar todos os chats
        this.channels.forEach((_, appointmentId) => {
          // Note: precisaríamos do userId aqui, que não temos no contexto
          // Este seria um caso para reconexão manual pelo hook
        });
      }
    };

    window.addEventListener('online', updateOnlineStatus);
    window.addEventListener('offline', updateOnlineStatus);
  }

  private startLatencyMonitoring(): void {
    // Monitoramento de latência otimizado para mobile (menos frequente para economizar bateria)
    this.pingInterval = setInterval(async () => {
      // Só fazer ping se estiver conectado e online
      if (this.connectionStatus.status === 'connected' && this.connectionStatus.isOnline) {
        // Fazer ping apenas do primeiro canal ativo para economizar recursos
        const channels = Array.from(this.channels.entries());
        if (channels.length > 0) {
          const [appointmentId] = channels[0];
          try {
            const latency = await this.pingConnection(appointmentId);
            if (latency !== null) {
              this.connectionStatus.latency = latency;
            }
          } catch (error) {
            console.warn(`[SupabaseRealtime] Erro no ping para ${appointmentId}:`, error);
            // Em caso de erro, pode indicar problema de conectividade
            if (this.connectionStatus.reconnectAttempts < this.maxReconnectAttempts) {
              console.log('[SupabaseRealtime] Ping falhou, verificando conectividade...');
            }
          }
        }
      }
    }, 45000); // Ping a cada 45 segundos (otimizado para mobile)
  }

  private setupConnectionMonitoring(): void {
    // Monitoramento adicional de estabilidade da conexão
    setInterval(() => {
      const channels = Array.from(this.channels.entries());

      // Verificar se há canais órfãos (sem status válido)
      channels.forEach(([appointmentId, channel]) => {
        if (channel.state !== 'joined' && channel.state !== 'joining') {
          console.warn(`[SupabaseRealtime] Canal ${appointmentId} em estado inválido: ${channel.state}`);
          // Tentar reconectar automaticamente
          this.reconnectChannel(appointmentId);
        }
      });

      // Verificar se há muitas tentativas de reconexão
      if (this.connectionStatus.reconnectAttempts > this.maxReconnectAttempts) {
        console.error('[SupabaseRealtime] Muitas tentativas de reconexão, pausando...');
        this.connectionStatus.status = 'error';
        this.connectionStatus.lastError = 'Muitas tentativas de reconexão falharam';
      }
    }, 10000); // Verificar a cada 10 segundos
  }

  private async reconnectChannel(appointmentId: string): Promise<void> {
    const channel = this.channels.get(appointmentId);
    if (!channel) return;

    try {
      console.log(`[SupabaseRealtime] Tentando reconectar canal ${appointmentId}`);
      await channel.unsubscribe();
      this.channels.delete(appointmentId);

      // Pequeno delay antes de reconectar
      setTimeout(async () => {
        try {
          await this.connectToChat(appointmentId, 'reconnect');
        } catch (error) {
          console.error(`[SupabaseRealtime] Erro ao reconectar canal ${appointmentId}:`, error);
        }
      }, 2000);
    } catch (error) {
      console.error(`[SupabaseRealtime] Erro ao desconectar canal ${appointmentId}:`, error);
    }
  }
}

// Instância singleton
let realtimeServiceInstance: SupabaseRealtimeService | null = null;

export function getRealtimeService(): SupabaseRealtimeService {
  if (!realtimeServiceInstance) {
    realtimeServiceInstance = new SupabaseRealtimeService();
  }
  return realtimeServiceInstance;
}

export function destroyRealtimeService(): void {
  if (realtimeServiceInstance) {
    realtimeServiceInstance.destroy();
    realtimeServiceInstance = null;
  }
}
