import { ActiveConsultation } from "../../actions/chats/get-active-consultations";

export interface Consultation extends ActiveConsultation {}

export class ConsultationService {
  private baseUrl = '/api';

  async getActiveConsultations(
    userId: string, 
    userRole: 'doctor' | 'patient',
    options: { page?: number; limit?: number; status?: string } = {}
  ): Promise<{ consultations: Consultation[]; total: number; hasMore: boolean }> {
    try {
      const { page = 1, limit = 20, status } = options;
      console.log(`[ConsultationService] Buscando consultas para userId: ${userId}, role: ${userRole}, page: ${page}, limit: ${limit}`);

      const params = new URLSearchParams({
        userId,
        role: userRole,
        page: page.toString(),
        limit: limit.toString(),
        ...(status && { status })
      });

      const response = await fetch(`${this.baseUrl}/consultations?${params}`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      if (!result.success || result.error) {
        throw new Error(result.error || 'Erro ao buscar consultas');
      }

      if (!result.consultations) {
        console.log("[ConsultationService] Nenhuma consulta encontrada");
        return { consultations: [], total: 0, hasMore: false };
      }

      console.log(`[ConsultationService] ${result.consultations.length} consultas carregadas do banco (página ${page})`);
      return {
        consultations: result.consultations,
        total: result.total || result.consultations.length,
        hasMore: result.hasMore || false
      };

    } catch (error) {
      console.error('[ConsultationService] Failed to fetch consultations:', error);
      throw new Error(`Failed to fetch consultations: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async startConsultation(consultationId: string): Promise<void> {
    try {
      console.log(`[ConsultationService] Iniciando consulta: ${consultationId}`);

      // Por enquanto, usar um userId mockado para testes
      const response = await fetch(`${this.baseUrl}/appointments/${consultationId}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: 'IN_PROGRESS',
          userId: 'mock-user-id'
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      if (!result.success || result.error) {
        throw new Error(result.error || 'Failed to start consultation');
      }

      console.log(`[ConsultationService] Consulta ${consultationId} iniciada com sucesso`);

    } catch (error) {
      console.error('[ConsultationService] Failed to start consultation:', error);
      throw new Error(`Failed to start consultation: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async endConsultation(consultationId: string): Promise<void> {
    try {
      console.log(`[ConsultationService] Finalizando consulta: ${consultationId}`);

      // Por enquanto, usar um userId mockado para testes
      const response = await fetch(`${this.baseUrl}/appointments/${consultationId}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: 'COMPLETED',
          userId: 'mock-user-id'
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      if (!result.success || result.error) {
        throw new Error(result.error || 'Failed to end consultation');
      }

      console.log(`[ConsultationService] Consulta ${consultationId} finalizada com sucesso`);

    } catch (error) {
      console.error('[ConsultationService] Failed to end consultation:', error);
      throw new Error(`Failed to end consultation: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async getConsultationById(consultationId: string): Promise<Consultation | null> {
    try {
      console.log(`[ConsultationService] Buscando consulta por ID: ${consultationId}`);

      // Por enquanto, retornar uma consulta mockada
      const mockConsultation: Consultation = {
        id: consultationId,
        doctor_name: 'Dr. Teste',
        patient_name: 'Paciente Teste',
        status: 'SCHEDULED',
        scheduled_at: new Date().toISOString(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      console.log(`[ConsultationService] Retornando consulta mockada:`, mockConsultation);
      return mockConsultation;

    } catch (error) {
      console.error('[ConsultationService] Failed to fetch consultation:', error);
      throw new Error(`Failed to fetch consultation: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}
