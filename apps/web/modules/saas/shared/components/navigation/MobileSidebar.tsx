import {
	<PERSON>er,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON>er<PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>er,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON>er<PERSON>rigger,
} from './Drawer';

import { Button } from '@ui/components/button';

import {
	Calendar1Icon,
	CalendarClock,
	HelpCircleIcon,
	Hospital,
	LayoutDashboardIcon,
	MenuIcon,
	Stethoscope,
	TagsIcon,
	UserRoundCog,
	UsersIcon,
	LifeBuoy,
	Siren,
	BanknoteIcon,
} from 'lucide-react';

import { Link } from '@i18n/routing';
import { useUser } from '@saas/auth/hooks/use-user';
import { siteConfig } from '@saas/shared/siteConfig';
import { Logo } from '@shared/components/Logo';
import { UserRoleSchema } from 'database';
import { usePathname } from 'next/navigation';
import { cx } from './utils';

export default function MobileSidebar() {
	const { user } = useUser();
	const pathname = usePathname();
	const isActive = (itemHref: string) => {
		if (itemHref === siteConfig.baseLinks.settings.audit) {
			return pathname.startsWith('/settings');
		}
		// Special handling for finance pages
		if (itemHref === '/app/finance') {
			return pathname.startsWith('/app/finance');
		}
		return pathname === itemHref || pathname.startsWith(itemHref);
	};

	const isAdmin = user?.role === UserRoleSchema.Values.ADMIN;
	const isDoctor = user?.role === UserRoleSchema.Values.DOCTOR;
	const isPatient = user?.role === UserRoleSchema.Values.PATIENT;
	const isSecretary = user?.role === UserRoleSchema.Values.SECRETARY;

	const navigation = [
		// Dashboard - Todos veem
		{
			name: 'Início',
			href: '/app/dashboard',
			icon: LayoutDashboardIcon,
			show: true,
		},

		// Plantão - Apenas Médico (PRIORIDADE 2)
		{
			name: 'Plantão',
			href: '/app/plantao',
			icon: Siren,
			show: isDoctor,
		},

		// Consultas - Médico, Paciente e Secretária
		{
			name: 'Consultas',
			href: '/app/appointments',
			icon: CalendarClock,
			show: isDoctor || isPatient || isSecretary,
		},

		// Agenda - Médico e Secretária
		{
			name: 'Agenda',
			href: '/app/schedule',
			icon: Calendar1Icon,
			show: isDoctor || isSecretary || isAdmin,
		},

		// Pacientes - Médico e Secretária
		{
			name: 'Pacientes',
			href: '/app/patients',
			icon: UsersIcon,
			show: isDoctor || isSecretary || isAdmin,
		},

		// Médicos - Apenas Admin
		{
			name: 'Médicos',
			href: '/app/doctors',
			icon: Stethoscope,
			show: isAdmin,
		},

		// Especialidades - Médico e Admin
		{
			name: 'Especialidades',
			href: '/app/specialties',
			icon: TagsIcon,
			show: isAdmin,
		},

		// Financeiro - Médicos e Admins
		{
			name: 'Financeiro',
			href: '/app/finance',
			icon: BanknoteIcon,
			show: isAdmin || isDoctor,
		},

		// Configurações - Todos veem
		{
			name: 'Configurações',
			href: '/app/settings',
			icon: UserRoundCog,
			show: true,
		},

		// Ajuda - Todos veem
		{
			name: 'Ajuda',
			href: '/app/help',
			icon: HelpCircleIcon,
			show: true,
		},
	] as const;

	return (
		<>
			<Drawer>
				<DrawerTrigger asChild>
					<Button
						variant='ghost'
						aria-label='open sidebar'
						className='group flex items-center rounded-md p-1.5 font-medium text-sm hover:bg-gray-100 data-[state=open]:bg-gray-100 data-[state=open]:bg-gray-400/10 hover:dark:bg-gray-400/10'
					>
						<MenuIcon
							className='size-6 shrink-0 text-gray-600 dark:text-gray-400'
							aria-hidden='true'
						/>
					</Button>
				</DrawerTrigger>
				<DrawerContent className='sm:max-w-lg'>
					<DrawerHeader>
						<DrawerTitle>
							<Logo />
						</DrawerTitle>
					</DrawerHeader>
					<DrawerBody>
						<nav
							aria-label='core mobile navigation links'
							className='flex flex-1 flex-col space-y-10'
						>
							<div>
								<ul role='list' className='mt-1 space-y-1.5'>
									{navigation
										.filter((item) => item.show)
										.map((item) => (
											<li key={item.name}>
												<DrawerClose asChild>
													<Link
														href={item.href}
														className={cx(
															isActive(item.href)
																? 'text-blue-600 dark:text-blue-500'
																: 'text-gray-600 hover:text-gray-900 dark:text-gray-400 hover:dark:text-gray-50',
															'flex items-center gap-x-2.5 rounded-md px-2 py-1.5 text-base font-medium transition hover:bg-gray-100 sm:text-sm hover:dark:bg-gray-900'
														)}
													>
														<item.icon
															className='size-5 shrink-0'
															aria-hidden='true'
														/>
														{item.name}
													</Link>
												</DrawerClose>
											</li>
										))}
								</ul>
							</div>
						</nav>
					</DrawerBody>
				</DrawerContent>
			</Drawer>
		</>
	);
}
