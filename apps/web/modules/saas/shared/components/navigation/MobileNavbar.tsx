'use client';

import { Link } from '@i18n/routing';
import { useUser } from '@saas/auth/hooks/use-user';
import { UserRoleSchema } from 'database';
import { usePathname } from 'next/navigation';
import { cx } from './utils';
import {
	LayoutDashboardIcon,
	CalendarClock,
	Calendar1Icon,
	UsersIcon,
	UserRoundCog,
	Stethoscope,
	MessageCircle,
	LifeBuoy,
	BanknoteIcon,
} from 'lucide-react';

export function MobileNavbar() {
	const { user } = useUser();
	const pathname = usePathname();

	if (!user) return null;

	// Não exibir a barra de navegação na página de chat
	const isChatPage = pathname.includes('/app/zapchat');
	if (isChatPage) return null;

	const isAdmin = user?.role === UserRoleSchema.Values.ADMIN;
	const isDoctor = user?.role === UserRoleSchema.Values.DOCTOR;
	const isPatient = user?.role === UserRoleSchema.Values.PATIENT;
	const isSecretary = user?.role === UserRoleSchema.Values.SECRETARY;

	const isActive = (itemHref: string) => {
		// Special handling for finance pages
		if (itemHref === '/app/finance') {
			return pathname.startsWith('/app/finance');
		}
		return pathname === itemHref || pathname.startsWith(itemHref);
	};

	// Definir os itens de navegação com base no papel do usuário
	const navItems = [
		// Dashboard - Todos veem
		{
			name: 'Início',
			href: '/app/dashboard',
			icon: LayoutDashboardIcon,
			show: true,
		},

		// Plantão - Apenas Médico (PRIORIDADE 2)
		{
			name: 'Plantão',
			href: '/app/plantao',
			icon: LifeBuoy,
			show: isDoctor,
		},

		// Chat - Todos veem (PRIORIDADE 3)
		{
			name: 'ZapChat',
			href: '/app/zapchat',
			icon: MessageCircle,
			show: true,
		},

		// Consultas - Médico, Paciente e Secretária
		{
			name: 'Consultas',
			href: '/app/appointments',
			icon: CalendarClock,
			show: isDoctor || isPatient || isSecretary,
		},

		// Agenda - Médico e Secretária
		{
			name: 'Agenda',
			href: '/app/schedule',
			icon: Calendar1Icon,
			show: isDoctor || isSecretary || isAdmin,
		},

		// Pacientes - Médico e Secretária
		{
			name: 'Pacientes',
			href: '/app/patients',
			icon: UsersIcon,
			show: isDoctor || isSecretary || isAdmin,
		},

		// Médicos - Apenas Admin
		{
			name: 'Médicos',
			href: '/app/doctors',
			icon: Stethoscope,
			show: isAdmin,
		},

		// Financeiro - Médicos e Admins
		{
			name: 'Financeiro',
			href: '/app/finance',
			icon: BanknoteIcon,
			show: isAdmin || isDoctor,
		},

		// Configurações - Todos veem
		{
			name: 'Perfil',
			href: '/app/settings',
			icon: UserRoundCog,
			show: true,
		},
	];

	// Filtrar apenas os itens que devem ser exibidos
	const visibleItems = navItems.filter((item) => item.show).slice(0, 5);

	return (
		<div className='fixed bottom-0 left-0 right-0 z-50 bg-white border-t border-gray-200 dark:bg-gray-950 dark:border-gray-800 lg:hidden'>
			<div className='flex justify-around items-center h-16'>
				{visibleItems.map((item) => (
					<Link
						key={item.name}
						href={item.href}
						className={cx(
							'flex flex-col items-center justify-center w-full h-full px-2 py-1',
							isActive(item.href)
								? 'text-primary'
								: 'text-gray-600 dark:text-gray-400'
						)}
					>
						<item.icon className='h-5 w-5' />
						<span className='text-xs mt-1'>{item.name}</span>
					</Link>
				))}
			</div>
		</div>
	);
}
